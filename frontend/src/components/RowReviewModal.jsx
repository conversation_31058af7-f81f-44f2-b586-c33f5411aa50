import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Badge } from "./ui/badge";
import { Plus, Trash2, Save, X } from "lucide-react";
import { toast } from "../hooks/use-toast";

const RowReviewModal = ({ isOpen, onClose, rowData, onSave }) => {
  const [editedData, setEditedData] = useState({
    OriginalProductName: "",
    BaseProductName: "",
    Option1Name: "",
    Option1Value: "",
    Option2Name: "",
    Option2Value: "", 
    Option3Name: "",
    Option3Value: "",
    originalIndex: null
  });

  const [options, setOptions] = useState([
    { name: "", value: "", id: "option1" },
    { name: "", value: "", id: "option2" },
    { name: "", value: "", id: "option3" }
  ]);

  useEffect(() => {
    if (rowData) {
      setEditedData(rowData);
      setOptions([
        { 
          name: rowData.Option1Name || "", 
          value: rowData.Option1Value || "", 
          id: "option1" 
        },
        { 
          name: rowData.Option2Name || "", 
          value: rowData.Option2Value || "", 
          id: "option2" 
        },
        { 
          name: rowData.Option3Name || "", 
          value: rowData.Option3Value || "", 
          id: "option3" 
        }
      ]);
    }
  }, [rowData]);

  const handleInputChange = (field, value) => {
    setEditedData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleOptionChange = (index, field, value) => {
    const newOptions = [...options];
    newOptions[index][field] = value;
    
    // Update the edited data with the new option values
    const updatedData = { ...editedData };
    updatedData[`Option${index + 1}Name`] = newOptions[index].name;
    updatedData[`Option${index + 1}Value`] = newOptions[index].value;
    
    setOptions(newOptions);
    setEditedData(updatedData);
  };

  // Function to compact options when user finishes editing (onBlur)
  const handleOptionBlur = () => {
    const compactedOptions = compactOptions(options);
    setOptions(compactedOptions);
    
    // Update editedData with compacted options
    const updatedData = { ...editedData };
    updatedData.Option1Name = compactedOptions[0]?.name || "";
    updatedData.Option1Value = compactedOptions[0]?.value || "";
    updatedData.Option2Name = compactedOptions[1]?.name || "";
    updatedData.Option2Value = compactedOptions[1]?.value || "";
    updatedData.Option3Name = compactedOptions[2]?.name || "";
    updatedData.Option3Value = compactedOptions[2]?.value || "";
    
    setEditedData(updatedData);
  };

  const addOption = () => {
    if (options.length < 5) {
      const newOptions = [...options, { 
        name: "", 
        value: "", 
        id: `option${options.length + 1}` 
      }];
      setOptions(newOptions);
    }
  };

  const removeOption = (index) => {
    if (options.length > 1) {
      // Remove the option at the specified index
      const newOptions = options.filter((_, i) => i !== index);
      
      // Compact the remaining options to remove gaps
      const compactedOptions = compactOptions(newOptions);
      
      setOptions(compactedOptions);
      
      // Update editedData with compacted options
      const updatedData = { ...editedData };
      updatedData.Option1Name = compactedOptions[0]?.name || "";
      updatedData.Option1Value = compactedOptions[0]?.value || "";
      updatedData.Option2Name = compactedOptions[1]?.name || "";
      updatedData.Option2Value = compactedOptions[1]?.value || "";
      updatedData.Option3Name = compactedOptions[2]?.name || "";
      updatedData.Option3Value = compactedOptions[2]?.value || "";
      
      setEditedData(updatedData);
    }
  };

  // Function to compact options by removing gaps
  const compactOptions = (optionsArray) => {
    // Filter out empty options (both name and value empty)
    const validOptions = optionsArray.filter(option => 
      (option.name && option.name.trim()) || (option.value && option.value.trim())
    );
    
    // Create compacted array with proper sequential numbering
    const compacted = [];
    validOptions.forEach((option, index) => {
      compacted.push({
        name: option.name ? option.name.trim() : "",
        value: option.value ? option.value.trim() : "",
        id: `option${index + 1}`
      });
    });
    
    // Fill remaining slots with empty options up to 3 total
    while (compacted.length < 3) {
      compacted.push({
        name: "",
        value: "",
        id: `option${compacted.length + 1}`
      });
    }
    
    return compacted;
  };

  const handleSave = () => {
    // Validate required fields
    if (!editedData.OriginalProductName.trim()) {
      toast({
        title: "Validation Error",
        description: "Original Product Name is required.",
        variant: "destructive"
      });
      return;
    }

    if (!editedData.BaseProductName.trim()) {
      toast({
        title: "Validation Error", 
        description: "Base Product Name is required.",
        variant: "destructive"
      });
      return;
    }

    // Compact options to remove gaps
    const compactedOptions = compactOptions(options);

    // Prepare the final data with compacted options
    const finalData = {
      ...editedData,
      Option1Name: compactedOptions[0]?.name || "",
      Option1Value: compactedOptions[0]?.value || "",
      Option2Name: compactedOptions[1]?.name || "",
      Option2Value: compactedOptions[1]?.value || "",
      Option3Name: compactedOptions[2]?.name || "",
      Option3Value: compactedOptions[2]?.value || "",
    };

    onSave(finalData);
  };

  const handleClose = () => {
    onClose();
  };

  if (!rowData) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <span>Edit Product Variant</span>
            <Badge variant="outline" className="text-xs">
              Row #{(rowData.originalIndex || 0) + 1}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Make changes to the product information and options below.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Basic Product Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Product Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="original-name" className="text-sm font-medium">
                    Original Product Name *
                  </Label>
                  <Input
                    id="original-name"
                    value={editedData.OriginalProductName}
                    onChange={(e) => handleInputChange("OriginalProductName", e.target.value)}
                    placeholder="Enter original product name"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="base-name" className="text-sm font-medium">
                    Base Product Name *
                  </Label>
                  <Input
                    id="base-name"
                    value={editedData.BaseProductName}
                    onChange={(e) => handleInputChange("BaseProductName", e.target.value)}
                    placeholder="Enter base product name"
                    className="mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Options */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Product Options</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addOption}
                  disabled={options.length >= 5}
                  className="text-xs"
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Add Option
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {options.map((option, index) => (
                <div 
                  key={option.id} 
                  className="flex items-end space-x-3 p-3 rounded-lg border bg-muted/30"
                >
                  <div className="flex-1">
                    <Label 
                      htmlFor={`option-name-${index}`}
                      className="text-sm font-medium"
                    >
                      Option {index + 1} Name
                    </Label>
                    <Input
                      id={`option-name-${index}`}
                      value={option.name}
                      onChange={(e) => handleOptionChange(index, "name", e.target.value)}
                      onBlur={handleOptionBlur}
                      placeholder={`e.g., Color, Size, Material`}
                      className="mt-1"
                    />
                  </div>
                  <div className="flex-1">
                    <Label 
                      htmlFor={`option-value-${index}`}
                      className="text-sm font-medium"
                    >
                      Option {index + 1} Value
                    </Label>
                    <Input
                      id={`option-value-${index}`}
                      value={option.value}
                      onChange={(e) => handleOptionChange(index, "value", e.target.value)}
                      onBlur={handleOptionBlur}
                      placeholder={`e.g., Red, Large, Cotton`}
                      className="mt-1"
                    />
                  </div>
                  {options.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeOption(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
              
              {options.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No options added yet.</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addOption}
                    className="mt-2"
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    Add First Option
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex items-center justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="flex items-center space-x-1"
          >
            <X className="w-4 h-4" />
            <span>Cancel</span>
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            className="flex items-center space-x-1"
          >
            <Save className="w-4 h-4" />
            <span>Save Changes</span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RowReviewModal;