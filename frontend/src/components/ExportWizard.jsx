import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Checkbox } from "./ui/checkbox";
import <PERSON> from "papaparse";
import { compactAllData, getDataStatistics } from "../utils/dataUtils";
import { 
  Download, 
  FileText, 
  CheckCircle2, 
  BarChart3,
  Calendar,
  Package,
  TrendingUp,
  Target,
  AlertCircle,
  Info
} from "lucide-react";
import { toast } from "../hooks/use-toast";

const ExportWizard = ({ 
  data, 
  rejectedRows, 
  reviewedRows, 
  merchantType,
  onComplete 
}) => {
  const [exportOptions, setExportOptions] = useState({
    includeAll: true,
    includeReviewed: true,
    includeRejected: false,
    includeOriginal: false
  });

  const getMerchantTypeName = () => {
    const types = {
      'food-meat': 'Food & Meat Products',
      'bicycle-sports': 'Bicycle & Sports Footwear',
      'jewelry': 'Jewelry',
      'apparel': 'Apparel',
      'wine': 'Wine',
      'cbd': 'CBD Products'
    };
    return types[merchantType] || 'Unknown';
  };

  const getExportStats = () => {
    return {
      total: data.length,
      reviewed: reviewedRows.length,
      rejected: rejectedRows.length,
      pending: data.length - reviewedRows.length - rejectedRows.length
    };
  };

  const generateFileName = (type) => {
    const timestamp = new Date().toISOString().slice(0, 10);
    const merchantPrefix = merchantType.replace('-', '_');
    return `${merchantPrefix}_products_${type}_${timestamp}.csv`;
  };

  const downloadCsv = (exportData, filename) => {
    if (exportData.length === 0) {
      toast({
        title: "No Data to Export",
        description: "The selected export criteria returned no data.",
        variant: "destructive"
      });
      return;
    }

    // Apply compaction to ensure no gaps in options before export
    const compactedData = compactAllData(exportData);

    // Get all unique headers from the data to preserve additional columns
    const allHeaders = new Set();
    compactedData.forEach(row => {
      Object.keys(row).forEach(key => {
        if (key !== 'originalIndex') { // Exclude internal tracking fields
          allHeaders.add(key);
        }
      });
    });

    // Ensure standard headers are included in proper order
    const orderedHeaders = [
      'OriginalProductName',
      'BaseProductName',
      'Option1Name',
      'Option1Value',
      'Option2Name',
      'Option2Value',
      'Option3Name',
      'Option3Value'
    ];

    // Add any additional headers that aren't in the standard set
    const additionalHeaders = Array.from(allHeaders).filter(header => 
      !orderedHeaders.includes(header)
    ).sort();

    const finalHeaders = [...orderedHeaders, ...additionalHeaders];

    // Clean data for export with all headers
    const csvData = compactedData.map(row => {
      const cleanRow = {};
      finalHeaders.forEach(header => {
        cleanRow[header] = row[header] || "";
      });
      return cleanRow;
    });

    const csv = Papa.unparse(csvData, {
      header: true,
      skipEmptyLines: false,
      columns: finalHeaders
    });

    // Create download
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `${filename} has been downloaded with all ${finalHeaders.length} columns and compacted options.`,
      });
    }
  };

  const handleExportAll = () => {
    downloadCsv(data, generateFileName('complete'));
  };

  const handleExportReviewed = () => {
    downloadCsv(reviewedRows, generateFileName('reviewed'));
  };

  const handleExportRejected = () => {
    downloadCsv(rejectedRows, generateFileName('rejected'));
  };

  const handleCustomExport = () => {
    let exportData = [];
    
    if (exportOptions.includeAll) {
      exportData = [...data];
    } else {
      if (exportOptions.includeReviewed) {
        exportData = [...exportData, ...reviewedRows];
      }
      if (exportOptions.includeRejected) {
        exportData = [...exportData, ...rejectedRows];
      }
      if (exportOptions.includeOriginal) {
        // Get pending/unprocessed rows
        const processedIndexes = new Set([
          ...reviewedRows.map(r => r.originalIndex),
          ...rejectedRows.map(r => r.originalIndex)
        ]);
        const pendingRows = data.filter((_, index) => !processedIndexes.has(index));
        exportData = [...exportData, ...pendingRows];
      }
    }

    // Remove duplicates based on original index
    const uniqueData = exportData.filter((item, index, self) => 
      index === self.findIndex(t => t.OriginalProductName === item.OriginalProductName)
    );

    downloadCsv(uniqueData, generateFileName('custom'));
  };

  const stats = getExportStats();
  const detailedStats = getDataStatistics(data, reviewedRows, rejectedRows);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Export Your Data
        </h2>
        <p className="text-muted-foreground">
          Download your processed product data in CSV format
        </p>
      </div>

      {/* Enhanced Export Summary with KPIs */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Stats */}
        <Card className="border-blue-200 bg-blue-50/30 dark:bg-blue-950/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-blue-900 dark:text-blue-100">
              <BarChart3 className="w-5 h-5" />
              <span>Processing Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{detailedStats.completionRate}%</div>
                <div className="text-sm text-muted-foreground">Completion Rate</div>
              </div>
              <div className="text-center">  
                <div className="text-2xl font-bold text-green-600">{stats.reviewed}</div>
                <div className="text-sm text-muted-foreground">Reviewed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
                <div className="text-sm text-muted-foreground">Rejected</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Package className="w-4 h-4" />
                <span>Business Type: {getMerchantTypeName()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>Processed: {new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Advanced KPIs */}
        <Card className="border-purple-200 bg-purple-50/30 dark:bg-purple-950/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-purple-900 dark:text-purple-100">
              <TrendingUp className="w-5 h-5" />
              <span>Data Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-foreground">{detailedStats.uniqueBaseProducts}</div>
                <div className="text-sm text-muted-foreground">Unique Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{detailedStats.avgOptionsPerProduct}</div>
                <div className="text-sm text-muted-foreground">Avg Options/Product</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{detailedStats.topOptionNames.length}</div>
                <div className="text-sm text-muted-foreground">Option Types</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-cyan-600">{detailedStats.optionStats.totalOptionsUsed}</div>
                <div className="text-sm text-muted-foreground">Total Options</div>
              </div>
            </div>

            {/* Top Option Names */}
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Top Option Types:</div>
              <div className="flex flex-wrap gap-1">
                {detailedStats.topOptionNames.slice(0, 3).map((option, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {option.name} ({option.count})
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Quality Insights */}
      {(detailedStats.dataQuality.emptyBaseProductNames > 0 || 
        detailedStats.dataQuality.incompleteOptions > 0) && (
        <Card className="border-amber-200 bg-amber-50/30 dark:bg-amber-950/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-amber-900 dark:text-amber-100">
              <AlertCircle className="w-5 h-5" />
              <span>Data Quality Alerts</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {detailedStats.dataQuality.emptyBaseProductNames > 0 && (
                <div className="flex items-center space-x-2 text-sm text-amber-700 dark:text-amber-300">
                  <Info className="w-4 h-4" />
                  <span>{detailedStats.dataQuality.emptyBaseProductNames} products have empty base names</span>
                </div>
              )}
              {detailedStats.dataQuality.incompleteOptions > 0 && (
                <div className="flex items-center space-x-2 text-sm text-amber-700 dark:text-amber-300">
                  <Info className="w-4 h-4" />
                  <span>{detailedStats.dataQuality.incompleteOptions} products have incomplete options</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Option Usage Breakdown */}
      <Card className="border-green-200 bg-green-50/30 dark:bg-green-950/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-green-900 dark:text-green-100">
            <Target className="w-5 h-5" />
            <span>Option Usage Distribution</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold text-foreground">{detailedStats.optionStats.option1Used}</div>
              <div className="text-sm text-muted-foreground">Products with Option 1</div>
              <div className="text-xs text-green-600">
                {Math.round((detailedStats.optionStats.option1Used / stats.total) * 100)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-foreground">{detailedStats.optionStats.option2Used}</div>
              <div className="text-sm text-muted-foreground">Products with Option 2</div>
              <div className="text-xs text-green-600">
                {Math.round((detailedStats.optionStats.option2Used / stats.total) * 100)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-foreground">{detailedStats.optionStats.option3Used}</div>
              <div className="text-sm text-muted-foreground">Products with Option 3</div>
              <div className="text-xs text-green-600">
                {Math.round((detailedStats.optionStats.option3Used / stats.total) * 100)}%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Export Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-green-200 hover:bg-green-50/50 transition-colors">
          <CardContent className="p-6 text-center">
            <CheckCircle2 className="w-8 h-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Complete Dataset</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Export all products with your latest changes
            </p>
            <Button 
              onClick={handleExportAll}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              <Download className="w-4 h-4 mr-2" />
              Export All ({stats.total})
            </Button>
          </CardContent>
        </Card>

        <Card className="border-blue-200 hover:bg-blue-50/50 transition-colors">
          <CardContent className="p-6 text-center">
            <FileText className="w-8 h-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Reviewed Only</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Export only products you've reviewed and edited
            </p>
            <Button 
              onClick={handleExportReviewed}
              disabled={stats.reviewed === 0}
              variant="outline"
              className="w-full border-blue-200"
            >
              <Download className="w-4 h-4 mr-2" />
              Export Reviewed ({stats.reviewed})
            </Button>
          </CardContent>
        </Card>

        <Card className="border-red-200 hover:bg-red-50/50 transition-colors">
          <CardContent className="p-6 text-center">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-red-600 text-lg font-bold">×</span>
            </div>
            <h3 className="font-semibold mb-2">Rejected Only</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Export products marked for rejection
            </p>
            <Button 
              onClick={handleExportRejected}
              disabled={stats.rejected === 0}
              variant="outline"
              className="w-full border-red-200"
            >
              <Download className="w-4 h-4 mr-2" />
              Export Rejected ({stats.rejected})
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Custom Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Export Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-all"
                checked={exportOptions.includeAll}
                onCheckedChange={(checked) => 
                  setExportOptions(prev => ({ 
                    ...prev, 
                    includeAll: checked,
                    includeReviewed: checked ? false : prev.includeReviewed,
                    includeRejected: checked ? false : prev.includeRejected,
                    includeOriginal: checked ? false : prev.includeOriginal
                  }))
                }
              />
              <label htmlFor="include-all" className="text-sm font-medium">
                Include all products (complete dataset)
              </label>
            </div>

            {!exportOptions.includeAll && (
              <div className="ml-6 space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-reviewed"
                    checked={exportOptions.includeReviewed}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeReviewed: checked }))
                    }
                  />
                  <label htmlFor="include-reviewed" className="text-sm">
                    Include reviewed products ({stats.reviewed})
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-rejected"
                    checked={exportOptions.includeRejected}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeRejected: checked }))
                    }
                  />
                  <label htmlFor="include-rejected" className="text-sm">
                    Include rejected products ({stats.rejected})
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="include-original"
                    checked={exportOptions.includeOriginal}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeOriginal: checked }))
                    }
                  />
                  <label htmlFor="include-original" className="text-sm">
                    Include unprocessed products ({stats.pending})
                  </label>
                </div>
              </div>
            )}
          </div>

          <Button 
            onClick={handleCustomExport}
            className="w-full"
            disabled={!exportOptions.includeAll && !exportOptions.includeReviewed && !exportOptions.includeRejected && !exportOptions.includeOriginal}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Custom Selection
          </Button>
        </CardContent>
      </Card>

      {/* Completion */}
      <div className="text-center pt-6">
        <Button 
          onClick={onComplete}
          variant="outline"
          size="lg"
        >
          Start New Review Process
        </Button>
        <p className="text-xs text-muted-foreground mt-2">
          This will reset all data and start from step 1
        </p>
      </div>
    </div>
  );
};

export default ExportWizard;