import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Switch } from "./ui/switch";
import { Badge } from "./ui/badge";
import { Label } from "./ui/label";
import FileUploader from "./FileUploader";
import ThemeToggle from "./ThemeToggle";
import MerchantTypeSelector from "./MerchantTypeSelector";
import QuickSetupMode from "./QuickSetupMode";
import OptionExtractor from "./OptionExtractor";
import OptionEditor from "./OptionEditor";
import DataTable from "./DataTable";
import ExportControls from "./ExportControls";
import ExportWizard from "./ExportWizard";
import StepWizard from "./StepWizard";
import RowReviewModal from "./RowReviewModal";
import { Toaster } from "./ui/toaster";
import { toast } from "../hooks/use-toast";
import { compactAllData, compactRowOptions } from "../utils/dataUtils";
import { 
  CheckCircle2, 
  Settings2, 
  Zap, 
  ChevronDown, 
  ChevronUp, 
  BarChart3, 
  Users, 
  Clock,
  ArrowRight
} from "lucide-react";

const CsvReviewApp = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [merchantType, setMerchantType] = useState('');
  const [csvData, setCsvData] = useState([]);
  const [rejectedRows, setRejectedRows] = useState([]);
  const [reviewedRows, setReviewedRows] = useState([]);
  const [editingRow, setEditingRow] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [setupMode, setSetupMode] = useState(() => {
    // Remember user's preferred mode
    const savedMode = localStorage.getItem('csvReviewTool_setupMode');
    return savedMode || 'simple';
  });
  const [isSetupCollapsed, setIsSetupCollapsed] = useState(false);
  const [hasProcessedData, setHasProcessedData] = useState(false);

  const totalSteps = 6;

  // Auto-save user preferences
  useEffect(() => {
    localStorage.setItem('csvReviewTool_setupMode', setupMode);
  }, [setupMode]);

  useEffect(() => {
    if (hasProcessedData) {
      localStorage.setItem('csvReviewTool_isSetupCollapsed', isSetupCollapsed.toString());
    }
  }, [isSetupCollapsed, hasProcessedData]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'k':
            e.preventDefault();
            // Focus search input if data table is visible
            if (csvData.length > 0) {
              const searchInput = document.querySelector('input[placeholder*="Search"]');
              searchInput?.focus();
            }
            break;
          case 's':
            e.preventDefault();
            setIsSetupCollapsed(!isSetupCollapsed);
            break;
          case 'm':
            e.preventDefault();
            setSetupMode(setupMode === 'simple' ? 'advanced' : 'simple');
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [csvData.length, isSetupCollapsed, setupMode]);

  // Generate consistent color for each BaseProductName
  const generateColor = (productName) => {
    if (!productName) return "bg-gray-50 dark:bg-gray-900";
    
    let hash = 0;
    for (let i = 0; i < productName.length; i++) {
      hash = productName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    const colors = [
      "bg-blue-50 dark:bg-blue-950",
      "bg-green-50 dark:bg-green-950", 
      "bg-yellow-50 dark:bg-yellow-950",
      "bg-purple-50 dark:bg-purple-950",
      "bg-pink-50 dark:bg-pink-950",
      "bg-indigo-50 dark:bg-indigo-950",
      "bg-red-50 dark:bg-red-950",
      "bg-orange-50 dark:bg-orange-950",
      "bg-teal-50 dark:bg-teal-950",
      "bg-cyan-50 dark:bg-cyan-950"
    ];
    
    return colors[Math.abs(hash) % colors.length];
  };

  const handleFileUpload = (data) => {
    // Apply compaction to uploaded data to ensure consistency
    const compactedData = compactAllData(data);
    setCsvData(compactedData);
    setRejectedRows([]);
    setReviewedRows([]);
    toast({
      title: "CSV Uploaded Successfully",
      description: `Loaded ${compactedData.length} products for review.`,
    });
  };

  const handleRejectRow = (rowIndex) => {
    const row = csvData[rowIndex];
    if (!rejectedRows.find(r => r.originalIndex === rowIndex)) {
      setRejectedRows([...rejectedRows, { ...row, originalIndex: rowIndex }]);
      toast({
        title: "Row Rejected",
        description: `${row.OriginalProductName} has been rejected.`,
        variant: "destructive"
      });
    }
  };

  const handleEditRow = (rowIndex) => {
    setEditingRow({ ...csvData[rowIndex], originalIndex: rowIndex });
    setIsModalOpen(true);
  };

  const handleSaveEdit = (editedData) => {
    // Compact the options to remove gaps before saving
    const compactedData = compactRowOptions(editedData);
    
    // Update the main data
    const newCsvData = [...csvData];
    newCsvData[compactedData.originalIndex] = compactedData;
    setCsvData(newCsvData);

    // Add to reviewed rows if not already there
    const existingReviewedIndex = reviewedRows.findIndex(
      r => r.originalIndex === compactedData.originalIndex
    );
    
    if (existingReviewedIndex >= 0) {
      const newReviewedRows = [...reviewedRows];
      newReviewedRows[existingReviewedIndex] = compactedData;
      setReviewedRows(newReviewedRows);
    } else {
      setReviewedRows([...reviewedRows, compactedData]);
    }

    // Remove from rejected if it was there
    setRejectedRows(rejectedRows.filter(r => r.originalIndex !== compactedData.originalIndex));

    setIsModalOpen(false);
    setEditingRow(null);
    
    toast({
      title: "Row Updated",
      description: `${compactedData.OriginalProductName} has been reviewed and saved.`,
    });
  };

  // Step navigation logic
  const canGoNext = () => {
    switch (currentStep) {
      case 1:
        return merchantType !== '';
      case 2:
        return csvData.length > 0;
      case 3:
        return true; // Can always proceed from option processing
      case 4:
        return true; // Can always proceed from data review
      case 5:
        return true; // Can always proceed from option editing
      case 6:
        return false; // Last step
      default:
        return false;
    }
  };

  const canGoPrevious = () => {
    return currentStep > 1;
  };

  const handleStepChange = (newStep) => {
    if (newStep >= 1 && newStep <= totalSteps) {
      setCurrentStep(newStep);
    }
  };

  const handleNextStep = () => {
    if (canGoNext()) {
      setCurrentStep(Math.min(currentStep + 1, totalSteps));
    }
  };

  const handleMerchantTypeSelect = (type) => {
    setMerchantType(type);
  };

  const handleFileUploadComplete = (data) => {
    // Apply compaction to uploaded data to ensure consistency
    const compactedData = compactAllData(data);
    setCsvData(compactedData);
    setRejectedRows([]);
    setReviewedRows([]);
    setHasProcessedData(false);
    toast({
      title: "CSV Uploaded Successfully",
      description: `Loaded ${compactedData.length} products for review.`,
    });
  };

  const handleCompleteWizard = () => {
    // Reset all state to start over
    setCurrentStep(1);
    setMerchantType('');
    setCsvData([]);
    setRejectedRows([]);
    setReviewedRows([]);
    setHasProcessedData(false);
    setIsSetupCollapsed(false);
    
    toast({
      title: "Process Reset",
      description: "Starting new review process from the beginning.",
    });
  };

  // New function to handle quick setup completion
  const handleQuickSetup = (processedData) => {
    // Apply compaction to processed data
    const compactedData = compactAllData(processedData);
    setCsvData(compactedData);
    
    // Reset review states since data has changed
    setRejectedRows([]);
    setReviewedRows([]);
    setHasProcessedData(true);
    
    toast({
      title: "Quick Setup Complete",
      description: `Applied automatic extraction rules based on your selections.`,
    });
  };

  // New function to handle option extraction
  const handleOptionExtraction = (extractedData) => {
    // Apply compaction to extracted data
    const compactedData = compactAllData(extractedData);
    setCsvData(compactedData);
    
    // Reset review states since data has changed
    setRejectedRows([]);
    setReviewedRows([]);
    setHasProcessedData(true);
    
    toast({
      title: "Options Processed Successfully",
      description: `Updated product names and removed specified option values.`,
    });
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <MerchantTypeSelector
            selectedType={merchantType}
            onTypeSelect={handleMerchantTypeSelect}
            onNext={handleNextStep}
          />
        );
      
      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">
                Upload Your CSV File
              </h2>
              <p className="text-muted-foreground">
                Upload your product data file to begin the review process
              </p>
            </div>
            <Card>
              <CardContent className="p-6">
                <FileUploader onFileUpload={handleFileUploadComplete} />
              </CardContent>
            </Card>
            {csvData.length > 0 && (
              <div className="text-center">
                <Button onClick={handleNextStep}>
                  Continue to Option Processing
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            )}
          </div>
        );
      
      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">
                Process Your Options
              </h2>
              <p className="text-muted-foreground">
                Configure how your product options should be processed
              </p>
            </div>
            
            {/* Mode Toggle */}
            <Card className="border-indigo-200 bg-indigo-50/30 dark:bg-indigo-950/20">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-indigo-600" />
                      <span className="font-medium text-indigo-900 dark:text-indigo-100">
                        Processing Mode
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Label htmlFor="mode-switch" className="text-sm font-medium">
                        Simple
                      </Label>
                      <Switch
                        id="mode-switch"
                        checked={setupMode === 'advanced'}
                        onCheckedChange={(checked) => setSetupMode(checked ? 'advanced' : 'simple')}
                      />
                      <Label htmlFor="mode-switch" className="text-sm font-medium">
                        Advanced
                      </Label>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {setupMode === 'simple' ? (
                      <span className="flex items-center">
                        <Zap className="w-4 h-4 mr-1 text-orange-500" />
                        Quick option selection
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Settings2 className="w-4 h-4 mr-1 text-blue-500" />
                        Full control & customization
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {setupMode === 'simple' && (
              <QuickSetupMode 
                data={csvData}
                onComplete={handleQuickSetup}
              />
            )}

            {setupMode === 'advanced' && (
              <OptionExtractor
                data={csvData}
                onExtract={handleOptionExtraction}
              />
            )}

            <div className="text-center">
              <Button onClick={handleNextStep}>
                Continue to Data Review
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        );
      
      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">
                Review Your Data
              </h2>
              <p className="text-muted-foreground">
                Visualize and validate your processed product data
              </p>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-foreground">
                    {csvData.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Products</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">
                    {reviewedRows.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Reviewed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-red-600">
                    {rejectedRows.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Rejected</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {csvData.length - reviewedRows.length - rejectedRows.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </CardContent>
              </Card>
            </div>

            {/* Data Table */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5" />
                    <span>Product Variants Review</span>
                  </CardTitle>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <span>{csvData.length} products</span>
                    {hasProcessedData && (
                      <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                        Processed
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <DataTable
                  data={csvData}
                  rejectedRows={rejectedRows}
                  reviewedRows={reviewedRows}
                  generateColor={generateColor}
                  onReject={handleRejectRow}
                  onEdit={handleEditRow}
                />
              </CardContent>
            </Card>

            <div className="text-center">
              <Button onClick={handleNextStep}>
                Continue to Option Editing
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        );
      
      case 5:
        return (
          <div className="space-y-6">
            <OptionEditor
              data={csvData}
              onDataUpdate={setCsvData}
            />
            <div className="text-center">
              <Button onClick={handleNextStep}>
                Continue to Export
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        );
      
      case 6:
        return (
          <ExportWizard
            data={csvData}
            rejectedRows={rejectedRows}
            reviewedRows={reviewedRows}
            merchantType={merchantType}
            onComplete={handleCompleteWizard}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Step Wizard Navigation */}
      <StepWizard
        currentStep={currentStep}
        totalSteps={totalSteps}
        onStepChange={handleStepChange}
        canGoNext={canGoNext()}
        canGoPrevious={canGoPrevious()}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6">
        {renderStepContent()}

        {/* Edit Modal (Available in all steps) */}
        <RowReviewModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditingRow(null);
          }}
          rowData={editingRow}
          onSave={handleSaveEdit}
        />

        <Toaster />
      </div>
    </div>
  );
};

export default CsvReviewApp;