import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";
import { 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  X,
  MoreVertical,
  Tags,
  Eye,
  Search
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { toast } from "../hooks/use-toast";

const OptionEditor = ({ data, onDataUpdate }) => {
  const [isAddingOption, setIsAddingOption] = useState(false);
  const [newOptionName, setNewOptionName] = useState('');
  const [editingOptions, setEditingOptions] = useState({});
  const [valueViewerO<PERSON>, setValueViewerOpen] = useState(false);
  const [selectedOptionForViewing, setSelectedOptionForViewing] = useState(null);

  // Component for viewing all values of an option
  const ValueViewer = ({ optionName, optionStats }) => {
    const [searchTerm, setSearchTerm] = useState('');
    
    const filteredValues = optionStats.values.filter(value =>
      value.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
      <Dialog open={valueViewerOpen} onOpenChange={setValueViewerOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Eye className="w-5 h-5" />
              <span>All Values for "{optionName}"</span>
            </DialogTitle>
            <DialogDescription>
              View all unique values for this option across your product catalog
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search values..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Stats */}
            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div className="text-sm">
                <span className="font-medium">{optionStats.count} products</span> use this option
              </div>
              <div className="text-sm text-muted-foreground">
                {filteredValues.length} of {optionStats.uniqueValues} values shown
              </div>
            </div>
            
            {/* Values Grid */}
            <div className="max-h-96 overflow-y-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredValues.map((value, index) => (
                  <div key={index} className="p-3 bg-background border rounded-lg">
                    <div className="text-sm font-medium">{value}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Used in {data.filter(row => 
                        (row.Option1Name === optionName && row.Option1Value === value) ||
                        (row.Option2Name === optionName && row.Option2Value === value) ||
                        (row.Option3Name === optionName && row.Option3Value === value)
                      ).length} products
                    </div>
                  </div>
                ))}
              </div>
              
              {filteredValues.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No values match your search</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Get current unique options
  const getCurrentOptions = () => {
    const options = new Set();
    data.forEach(row => {
      if (row.Option1Name && row.Option1Name.trim()) options.add(row.Option1Name.trim());
      if (row.Option2Name && row.Option2Name.trim()) options.add(row.Option2Name.trim());
      if (row.Option3Name && row.Option3Name.trim()) options.add(row.Option3Name.trim());
    });
    return Array.from(options).sort();
  };

  const getOptionStats = (optionName) => {
    const values = new Set();
    let count = 0;
    
    data.forEach(row => {
      ['Option1', 'Option2', 'Option3'].forEach(prefix => {
        if (row[`${prefix}Name`] === optionName && row[`${prefix}Value`]) {
          values.add(row[`${prefix}Value`]);
          count++;
        }
      });
    });
    
    return { count, uniqueValues: values.size, values: Array.from(values) };
  };

  const handleAddOption = () => {
    if (!newOptionName.trim()) {
      toast({
        title: "Invalid Option Name",
        description: "Please enter a valid option name.",
        variant: "destructive"
      });
      return;
    }

    const currentOptions = getCurrentOptions();
    if (currentOptions.includes(newOptionName.trim())) {
      toast({
        title: "Option Already Exists",
        description: "This option name is already in use.",
        variant: "destructive"
      });
      return;
    }

    // Add new option to products that have available slots
    const updatedData = data.map(row => {
      // Find first available option slot
      if (!row.Option1Name || !row.Option1Name.trim()) {
        return { ...row, Option1Name: newOptionName.trim(), Option1Value: '' };
      } else if (!row.Option2Name || !row.Option2Name.trim()) {
        return { ...row, Option2Name: newOptionName.trim(), Option2Value: '' };
      } else if (!row.Option3Name || !row.Option3Name.trim()) {
        return { ...row, Option3Name: newOptionName.trim(), Option3Value: '' };
      }
      return row;
    });

    onDataUpdate(updatedData);
    setNewOptionName('');
    setIsAddingOption(false);
    
    toast({
      title: "Option Added",
      description: `${newOptionName.trim()} has been added to available products.`,
    });
  };

  const handleRemoveOption = (optionName) => {
    if (!window.confirm(`Are you sure you want to remove the "${optionName}" option from all products? This action cannot be undone.`)) {
      return;
    }

    const updatedData = data.map(row => {
      let newRow = { ...row };
      
      ['Option1', 'Option2', 'Option3'].forEach(prefix => {
        if (newRow[`${prefix}Name`] === optionName) {
          newRow[`${prefix}Name`] = '';
          newRow[`${prefix}Value`] = '';
        }
      });
      
      return newRow;
    });

    onDataUpdate(updatedData);
    
    toast({
      title: "Option Removed",
      description: `${optionName} has been removed from all products.`,
      variant: "destructive"
    });
  };

  const handleRenameOption = (oldName, newName) => {
    if (!newName.trim() || newName.trim() === oldName) {
      setEditingOptions({});
      return;
    }

    const currentOptions = getCurrentOptions().filter(opt => opt !== oldName);
    if (currentOptions.includes(newName.trim())) {
      toast({
        title: "Option Name Taken",
        description: "This option name is already in use.",
        variant: "destructive"
      });
      return;
    }

    const updatedData = data.map(row => {
      let newRow = { ...row };
      
      ['Option1', 'Option2', 'Option3'].forEach(prefix => {
        if (newRow[`${prefix}Name`] === oldName) {
          newRow[`${prefix}Name`] = newName.trim();
        }
      });
      
      return newRow;
    });

    onDataUpdate(updatedData);
    setEditingOptions({});
    
    toast({
      title: "Option Renamed",
      description: `${oldName} has been renamed to ${newName.trim()}.`,
    });
  };

  const currentOptions = getCurrentOptions();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Edit Product Options
        </h2>
        <p className="text-muted-foreground">
          Add, remove, or rename option types across your product catalog
        </p>
      </div>

      {/* Add New Option */}
      <Card className="border-green-200 bg-green-50/30 dark:bg-green-950/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-green-900 dark:text-green-100">
            <Plus className="w-5 h-5" />
            <span>Add New Option Type</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!isAddingOption ? (
            <Button
              onClick={() => setIsAddingOption(true)}
              variant="outline"
              className="w-full border-green-200 text-green-700 hover:bg-green-100"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Option Type
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Input
                value={newOptionName}
                onChange={(e) => setNewOptionName(e.target.value)}
                placeholder="Enter option name (e.g., Material, Color, Size)"
                className="flex-1"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleAddOption();
                  if (e.key === 'Escape') {
                    setIsAddingOption(false);
                    setNewOptionName('');
                  }
                }}
                autoFocus
              />
              <Button onClick={handleAddOption} size="sm">
                <Save className="w-4 h-4" />
              </Button>
              <Button
                onClick={() => {
                  setIsAddingOption(false);
                  setNewOptionName('');
                }}
                variant="outline"
                size="sm"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Tags className="w-5 h-5" />
            <span>Current Option Types ({currentOptions.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentOptions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Tags className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No option types found in your data</p>
              <p className="text-sm">Add some options above to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentOptions.map((optionName) => {
                const stats = getOptionStats(optionName);
                const isEditing = editingOptions[optionName];
                
                return (
                  <Card key={optionName} className="border border-gray-200">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        {isEditing ? (
                          <div className="flex items-center space-x-2 flex-1">
                            <Input
                              defaultValue={optionName}
                              className="h-8 text-sm"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleRenameOption(optionName, e.target.value);
                                }
                                if (e.key === 'Escape') {
                                  setEditingOptions({});
                                }
                              }}
                              onBlur={(e) => {
                                handleRenameOption(optionName, e.target.value);
                              }}
                              autoFocus
                            />
                          </div>
                        ) : (
                          <>
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{optionName}</h4>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                  <MoreVertical className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => {
                                    setSelectedOptionForViewing({ name: optionName, stats });
                                    setValueViewerOpen(true);
                                  }}
                                >
                                  <Eye className="w-4 h-4 mr-2" />
                                  View All Values
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => setEditingOptions({ [optionName]: true })}
                                >
                                  <Edit3 className="w-4 h-4 mr-2" />
                                  Rename
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleRemoveOption(optionName)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Remove
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">Used in:</span>
                          <Badge variant="secondary" className="text-xs">
                            {stats.count} products
                          </Badge>
                        </div>
                        
                        <div className="text-xs text-muted-foreground">
                          <span className="font-medium">{stats.uniqueValues} unique values:</span>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {stats.values.slice(0, 3).map((value, index) => (
                              <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                                {value}
                              </Badge>
                            ))}
                            {stats.values.length > 3 && (
                              <Badge variant="outline" className="text-xs px-1 py-0">
                                +{stats.values.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Value Viewer Modal */}
      {selectedOptionForViewing && (
        <ValueViewer 
          optionName={selectedOptionForViewing.name}
          optionStats={selectedOptionForViewing.stats}
        />
      )}
    </div>
  );
};

export default OptionEditor;