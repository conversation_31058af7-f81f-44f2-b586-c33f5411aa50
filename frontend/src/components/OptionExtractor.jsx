import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, Card<PERSON>itle } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Checkbox } from "./ui/checkbox";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { 
  Settings, 
  Wand2, 
  Eye, 
  CheckCircle2, 
  ArrowRight, 
  Sparkles,
  Info,
  RefreshCw,
  X,
  Plus
} from "lucide-react";
import { toast } from "../hooks/use-toast";

const OptionExtractor = ({ data, onExtract }) => {
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [extractionRules, setExtractionRules] = useState([]);
  const [previewData, setPreviewData] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [draggedOption, setDraggedOption] = useState(null);

  // Get all unique option names from the data
  const getUniqueOptionNames = () => {
    const optionNames = new Set();
    data.forEach(row => {
      if (row.Option1Name && row.Option1Name.trim()) optionNames.add(row.Option1Name.trim());
      if (row.Option2Name && row.Option2Name.trim()) optionNames.add(row.Option2Name.trim());
      if (row.Option3Name && row.Option3Name.trim()) optionNames.add(row.Option3Name.trim());
    });
    return Array.from(optionNames).sort();
  };

  // Get option statistics
  const getOptionStats = () => {
    const stats = {};
    data.forEach(row => {
      ['Option1', 'Option2', 'Option3'].forEach(optionPrefix => {
        const name = row[`${optionPrefix}Name`];
        const value = row[`${optionPrefix}Value`];
        if (name && name.trim() && value && value.trim()) {
          if (!stats[name]) {
            stats[name] = { count: 0, values: new Set() };
          }
          stats[name].count++;
          stats[name].values.add(value);
        }
      });
    });
    
    return Object.entries(stats).map(([name, data]) => ({
      name,
      count: data.count,
      uniqueValues: data.values.size,
      values: Array.from(data.values).slice(0, 3) // Show first 3 values as preview
    })).sort((a, b) => b.count - a.count);
  };

  const addExtractionRule = (ruleType = 'extract', optionName = '') => {
    const uniqueOptions = getUniqueOptionNames();
    if (uniqueOptions.length > 0) {
      setExtractionRules([...extractionRules, {
        id: Date.now(),
        type: ruleType, // 'extract', 'remove', or 'rename'
        optionName: optionName,
        separator: ' - ',
        position: 'suffix',
        newName: '' // For rename operations
      }]);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (option) => {
    setDraggedOption(option);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDropExtract = (e) => {
    e.preventDefault();
    if (draggedOption) {
      addExtractionRule('extract', draggedOption.name);
      setDraggedOption(null);
      toast({
        title: "Option Added",
        description: `${draggedOption.name} added to Extract to Name rules.`,
      });
    }
  };

  const handleDropRemove = (e) => {
    e.preventDefault();
    if (draggedOption) {
      addExtractionRule('remove', draggedOption.name);
      setDraggedOption(null);
      toast({
        title: "Option Added",
        description: `${draggedOption.name} added to Remove Only rules.`,
      });
    }
  };

  const handleDropRename = (e) => {
    e.preventDefault();
    if (draggedOption) {
      addExtractionRule('rename', draggedOption.name);
      setDraggedOption(null);
      toast({
        title: "Option Added",
        description: `${draggedOption.name} added to Rename Option rules.`,
      });
    }
  };

  const removeExtractionRule = (id) => {
    setExtractionRules(extractionRules.filter(rule => rule.id !== id));
  };

  const updateExtractionRule = (id, field, value) => {
    setExtractionRules(extractionRules.map(rule => 
      rule.id === id ? { ...rule, [field]: value } : rule
    ));
  };

  // Toggle row selection
  const toggleRowSelection = (index) => {
    const newSelection = new Set(selectedRows);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedRows(newSelection);
  };

  // Select all rows
  const selectAllRows = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(data.map((_, index) => index)));
    }
  };

  // Generate preview of extraction
  const generatePreview = () => {
    if (extractionRules.length === 0) {
      toast({
        title: "No Extraction Rules",
        description: "Please add at least one extraction rule to preview changes.",
        variant: "destructive"
      });
      return;
    }

    const rowsToProcess = selectedRows.size > 0 
      ? data.filter((_, index) => selectedRows.has(index))
      : data;

    const processedData = data.map((row, index) => {
      if (selectedRows.size > 0 && !selectedRows.has(index)) {
        return row; // Don't process unselected rows
      }

      let newRow = { ...row };
      let newBaseProductName = row.BaseProductName || '';

      extractionRules.forEach(rule => {
        if (!rule.optionName) return;

        // Find the option to extract/remove/rename
        let optionValue = '';
        let optionFound = false;

        ['Option1', 'Option2', 'Option3'].forEach(optionPrefix => {
          const name = newRow[`${optionPrefix}Name`];
          const value = newRow[`${optionPrefix}Value`];
          
          if (name === rule.optionName && value && value.trim()) {
            optionValue = value.trim();
            optionFound = true;
            
            if (rule.type === 'extract') {
              // Extract: Remove the option from the row
              newRow[`${optionPrefix}Name`] = '';
              newRow[`${optionPrefix}Value`] = '';
            } else if (rule.type === 'remove') {
              // Remove: Remove the option from the row
              newRow[`${optionPrefix}Name`] = '';
              newRow[`${optionPrefix}Value`] = '';
            } else if (rule.type === 'rename' && rule.newName) {
              // Rename: Change the option name
              newRow[`${optionPrefix}Name`] = rule.newName;
              // Keep the value unchanged
            }
          }
        });

        // Add option value to base product name only if rule type is 'extract'
        if (optionFound && optionValue && rule.type === 'extract') {
          if (rule.position === 'prefix') {
            newBaseProductName = `${optionValue}${rule.separator}${newBaseProductName}`;
          } else {
            newBaseProductName = `${newBaseProductName}${rule.separator}${optionValue}`;
          }
        }
        // If rule type is 'remove', we just remove the option (already done above)
        // If rule type is 'rename', we just rename the option (already done above)
      });

      newRow.BaseProductName = newBaseProductName.trim();
      return newRow;
    });

    setPreviewData(processedData);
    setShowPreview(true);
    
    toast({
      title: "Preview Generated",
      description: `Generated preview for ${rowsToProcess.length} rows.`,
    });
  };

  // Apply the extraction
  const applyExtraction = () => {
    if (!previewData) {
      generatePreview();
      return;
    }

    onExtract(previewData);
    
    // Reset state
    setExtractionRules([]);
    setSelectedRows(new Set());
    setPreviewData(null);
    setShowPreview(false);
  };

  const optionStats = getOptionStats();
  const uniqueOptions = getUniqueOptionNames();

  return (
    <Card className="border-2 border-dashed border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/30">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Wand2 className="w-5 h-5 text-blue-600" />
            <CardTitle className="text-blue-900 dark:text-blue-100">
              Smart Option Extractor
            </CardTitle>
          </div>
          <Badge variant="outline" className="text-xs bg-blue-100 dark:bg-blue-900">
            Beta Feature
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Advanced mode: Extract option values and merge them into product names for better organization. Switch to Simple mode above for quick configuration.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <Tabs defaultValue="configure" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="configure" className="flex items-center space-x-1">
              <Settings className="w-4 h-4" />
              <span>Configure</span>
            </TabsTrigger>
            <TabsTrigger value="select" className="flex items-center space-x-1">
              <CheckCircle2 className="w-4 h-4" />
              <span>Select Rows</span>
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>Preview</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="configure" className="space-y-4 mt-4">
            <div className="flex gap-6">
              {/* Left Sidebar - Current Option Status */}
              <div className="w-1/3">
                <div className="bg-gradient-to-b from-blue-50 to-green-50 dark:from-blue-950/20 dark:to-green-950/20 p-4 rounded-xl border sticky top-4">
                  <h4 className="font-semibold text-foreground mb-3 flex items-center">
                    <Eye className="w-4 h-4 mr-2 text-blue-600" />
                    Current Option Status
                  </h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                    {optionStats.map((option, index) => (
                      <div key={index} className="p-3 bg-white dark:bg-gray-800 rounded-lg border shadow-sm">
                        <div className="text-center mb-2">
                          <div className="text-2xl font-bold text-foreground">
                            {option.count}
                          </div>
                          <div className="text-xs font-medium text-blue-600 dark:text-blue-400">
                            {option.name}
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground text-center">
                          {option.uniqueValues} {option.uniqueValues === 1 ? 'value' : 'values'}
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                          {option.values.join(', ')}
                          {option.values.length < option.uniqueValues && '...'}
                        </div>
                      </div>
                    ))}
                    {optionStats.length === 0 && (
                      <div className="text-center p-6 text-muted-foreground">
                        <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No options in current dataset</p>
                      </div>
                    )}
                  </div>
                  {optionStats.length > 0 && (
                    <div className="mt-3 p-2 bg-white dark:bg-gray-800 rounded text-center border-t">
                      <div className="text-xs text-muted-foreground">
                        <strong>Total:</strong> {optionStats.reduce((sum, option) => sum + option.count, 0)} instances
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <strong>Types:</strong> {optionStats.length} unique options
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Content Area */}
              <div className="flex-1 space-y-6">
                {/* Available Options with Drag Support */}
                <div className="space-y-3">
                  <h4 className="font-medium text-foreground">Available Options</h4>
                  <div className="text-xs text-muted-foreground mb-3">
                    💡 <strong>Tip:</strong> Drag options to Extract, Rename, or Remove zones for quick processing
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                    {optionStats.map((option, index) => (
                      <div 
                        key={index} 
                        className="p-3 bg-background rounded-lg border cursor-grab active:cursor-grabbing hover:shadow-md transition-shadow"
                        draggable="true"
                        onDragStart={() => handleDragStart(option)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                            <span className="font-medium text-sm">{option.name}</span>
                          </div>
                          <Badge variant="secondary" className="text-xs">
                            {option.count} items
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {option.uniqueValues} unique values: {option.values.join(', ')}
                          {option.values.length < option.uniqueValues && '...'}
                        </div>
                      </div>
                    ))}
                    {optionStats.length === 0 && (
                      <div className="col-span-full p-6 text-center text-muted-foreground border-2 border-dashed rounded-lg">
                        <Info className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No options available</p>
                        <p className="text-xs">Upload a CSV with option data to begin</p>
                      </div>
                    )}
                  </div>
                </div>

                </div>

                {/* Processing Rules with Drop Zones */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-foreground">Option Processing Rules</h4>
                  </div>
                
                {/* Drag and Drop Zones */}
                <div className="grid grid-cols-1 gap-3">
                  {/* Extract to Name Drop Zone */}
                  <div 
                    className={`p-4 border-2 border-dashed rounded-lg transition-all duration-200 ${
                      draggedOption 
                        ? 'border-blue-400 bg-blue-50 dark:bg-blue-950/20' 
                        : 'border-blue-200 bg-blue-50/30 dark:bg-blue-950/10 hover:border-blue-300'
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={handleDropExtract}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Sparkles className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                          Extract to Name
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => addExtractionRule('extract')}
                        className="text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                      {draggedOption ? 'Drop here to extract option to product name' : 'Drag options here or click + to add extraction rules'}
                    </div>
                  </div>

                  {/* Rename Option Drop Zone */}
                  <div 
                    className={`p-4 border-2 border-dashed rounded-lg transition-all duration-200 ${
                      draggedOption 
                        ? 'border-green-400 bg-green-50 dark:bg-green-950/20' 
                        : 'border-green-200 bg-green-50/30 dark:bg-green-950/10 hover:border-green-300'
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={handleDropRename}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <RefreshCw className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800 dark:text-green-200">
                          Rename Option
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => addExtractionRule('rename')}
                        className="text-green-600 hover:bg-green-100 dark:hover:bg-green-900"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-green-600 dark:text-green-300 mt-1">
                      {draggedOption ? 'Drop here to rename option to a new name' : 'Drag options here or click + to add rename rules'}
                    </div>
                  </div>

                  {/* Remove Only Drop Zone */}
                  <div 
                    className={`p-4 border-2 border-dashed rounded-lg transition-all duration-200 ${
                      draggedOption 
                        ? 'border-red-400 bg-red-50 dark:bg-red-950/20' 
                        : 'border-red-200 bg-red-50/30 dark:bg-red-950/10 hover:border-red-300'
                    }`}
                    onDragOver={handleDragOver}
                    onDrop={handleDropRemove}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <X className="w-4 h-4 text-red-600" />
                        <span className="text-sm font-medium text-red-800 dark:text-red-200">
                          Remove Only
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => addExtractionRule('remove')}
                        className="text-red-600 hover:bg-red-100 dark:hover:bg-red-900"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-red-600 dark:text-red-300 mt-1">
                      {draggedOption ? 'Drop here to remove option completely' : 'Drag options here or click + to add removal rules'}
                    </div>
                  </div>
                </div>

                {/* Configured Rules */}
                {extractionRules.length > 0 && (
                  <div className="space-y-3 mt-4">
                    <h5 className="text-sm font-medium text-muted-foreground">Configured Rules:</h5>
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {extractionRules.map((rule) => (
                        <div key={rule.id} className={`p-3 bg-background rounded-lg border space-y-3 ${
                          rule.type === 'extract' 
                            ? 'border-blue-200 bg-blue-50/30 dark:bg-blue-950/20' 
                            : rule.type === 'rename'
                            ? 'border-green-200 bg-green-50/30 dark:bg-green-950/20'
                            : 'border-red-200 bg-red-50/30 dark:bg-red-950/20'
                        }`}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              {rule.type === 'extract' ? (
                                <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                  <Sparkles className="w-3 h-3 mr-1" />
                                  Extract to Name
                                </Badge>
                              ) : rule.type === 'rename' ? (
                                <Badge variant="outline" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  <RefreshCw className="w-3 h-3 mr-1" />
                                  Rename Option
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                  <X className="w-3 h-3 mr-1" />
                                  Remove Only
                                </Badge>
                              )}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeExtractionRule(rule.id)}
                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            >
                              ×
                            </Button>
                          </div>
                          
                          <div className="grid grid-cols-1 gap-2">
                            <div>
                              <label className="text-xs text-muted-foreground">Option to Process</label>
                              <Select
                                value={rule.optionName}
                                onValueChange={(value) => updateExtractionRule(rule.id, 'optionName', value)}
                              >
                                <SelectTrigger className="h-8">
                                  <SelectValue placeholder="Select option" />
                                </SelectTrigger>
                                <SelectContent>
                                  {uniqueOptions.map((option) => (
                                    <SelectItem key={option} value={option}>
                                      {option}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          {/* Show new name field for rename rules */}
                          {rule.type === 'rename' && (
                            <div>
                              <label className="text-xs text-muted-foreground">New Option Name</label>
                              <Input
                                value={rule.newName || ''}
                                onChange={(e) => updateExtractionRule(rule.id, 'newName', e.target.value)}
                                placeholder="Enter new option name"
                                className="h-8 mt-1"
                              />
                            </div>
                          )}

                          {/* Show position and separator options only for extract rules */}
                          {rule.type === 'extract' && (
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <label className="text-xs text-muted-foreground">Position</label>
                                <Select
                                  value={rule.position}
                                  onValueChange={(value) => updateExtractionRule(rule.id, 'position', value)}
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="suffix">After name</SelectItem>
                                    <SelectItem value="prefix">Before name</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <label className="text-xs text-muted-foreground">Separator</label>
                                <Select
                                  value={rule.separator}
                                  onValueChange={(value) => updateExtractionRule(rule.id, 'separator', value)}
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value=" - "> - (dash)</SelectItem>
                                    <SelectItem value=" ">(space)</SelectItem>
                                    <SelectItem value=", ">, (comma)</SelectItem>
                                    <SelectItem value=" | "> | (pipe)</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          )}
                          
                          {rule.optionName && (
                            <div className="text-xs p-2 bg-muted rounded text-muted-foreground">
                              <strong>Preview:</strong> {
                                rule.type === 'extract' 
                                  ? `Base Product Name${rule.position === 'prefix' ? `${rule.separator}Option Value` : `${rule.separator}Option Value`}`
                                  : rule.type === 'rename'
                                  ? `Option "${rule.optionName}" will be renamed to "${rule.newName || '[New Name]'}"`
                                  : 'Option will be completely removed'
                              }
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {extractionRules.length === 0 && (
                  <div className="p-6 text-center text-muted-foreground border-2 border-dashed rounded-lg">
                    <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <div className="space-y-1">
                      <p className="text-sm">No processing rules configured</p>
                      <div className="text-xs space-y-1">
                        <p><strong>Drag & Drop:</strong> Drag options from left panel to drop zones above</p>
                        <p><strong>Manual:</strong> Click + buttons to add rules manually</p>
                        <p><strong>Actions:</strong> Extract to names, rename options, or remove entirely</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="select" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedRows.size === data.length}
                  onCheckedChange={selectAllRows}
                />
                <span className="text-sm font-medium">
                  Select All ({selectedRows.size}/{data.length} selected)
                </span>
              </div>
              <Badge variant="outline">
                {selectedRows.size === 0 ? 'All rows' : `${selectedRows.size} rows`} will be processed
              </Badge>
            </div>

            <div className="max-h-64 overflow-y-auto border rounded-lg">
              <div className="grid grid-cols-1 divide-y">
                {data.slice(0, 20).map((row, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 hover:bg-muted/50">
                    <Checkbox
                      checked={selectedRows.has(index)}
                      onCheckedChange={() => toggleRowSelection(index)}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {row.OriginalProductName}
                      </div>
                      <div className="text-xs text-muted-foreground truncate">
                        Base: {row.BaseProductName || 'N/A'}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      #{index + 1}
                    </div>
                  </div>
                ))}
                {data.length > 20 && (
                  <div className="p-3 text-center text-sm text-muted-foreground">
                    ... and {data.length - 20} more rows
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-start space-x-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <Info className="w-4 h-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800 dark:text-blue-200">
                <p><strong>Tip:</strong> If no rows are selected, all rows will be processed. Select specific rows to apply extraction rules only to those products.</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4 mt-4">
            {!showPreview ? (
              <div className="text-center py-8">
                <Eye className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground mb-4">Generate a preview to see how your extraction rules will affect the data</p>
                <Button onClick={generatePreview} disabled={extractionRules.length === 0}>
                  <Eye className="w-4 h-4 mr-2" />
                  Generate Preview
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Preview Changes</h4>
                  <Button variant="outline" size="sm" onClick={generatePreview}>
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Refresh Preview
                  </Button>
                </div>
                
                <div className="max-h-64 overflow-y-auto border rounded-lg">
                  {previewData?.slice(0, 10).map((newRow, index) => {
                    const originalRow = data[index];
                    const hasNameChanged = newRow.BaseProductName !== originalRow.BaseProductName;
                    const hasOptionsChanged = (
                      newRow.Option1Name !== originalRow.Option1Name ||
                      newRow.Option1Value !== originalRow.Option1Value ||
                      newRow.Option2Name !== originalRow.Option2Name ||
                      newRow.Option2Value !== originalRow.Option2Value ||
                      newRow.Option3Name !== originalRow.Option3Name ||
                      newRow.Option3Value !== originalRow.Option3Value
                    );
                    const hasChanged = hasNameChanged || hasOptionsChanged;
                    
                    if (!hasChanged && selectedRows.size > 0 && !selectedRows.has(index)) {
                      return null; // Don't show unchanged rows when specific rows are selected
                    }
                    
                    return (
                      <div key={index} className={`p-3 border-b last:border-b-0 ${hasChanged ? 'bg-green-50 dark:bg-green-950/20' : ''}`}>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium">{originalRow.OriginalProductName}</div>
                            <div className="text-xs text-muted-foreground">Row #{index + 1}</div>
                          </div>
                          
                          {hasNameChanged && (
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 items-center">
                              <div className="text-xs text-muted-foreground">Base Name:</div>
                              <div className="flex items-center space-x-2 text-sm">
                                <span className="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-xs">
                                  {originalRow.BaseProductName || 'N/A'}
                                </span>
                                <ArrowRight className="w-4 h-4 text-muted-foreground" />
                              </div>
                              <div className="text-sm">
                                <span className="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-xs">
                                  {newRow.BaseProductName}
                                </span>
                              </div>
                            </div>
                          )}

                          {hasOptionsChanged && (
                            <div className="text-xs">
                              <div className="text-muted-foreground mb-1">Option Changes:</div>
                              <div className="grid grid-cols-3 gap-2">
                                {['Option1', 'Option2', 'Option3'].map((optionPrefix) => {
                                  const originalName = originalRow[`${optionPrefix}Name`];
                                  const originalValue = originalRow[`${optionPrefix}Value`];
                                  const newName = newRow[`${optionPrefix}Name`];
                                  const newValue = newRow[`${optionPrefix}Value`];
                                  
                                  if (originalName && originalName !== newName) {
                                    return (
                                      <div key={optionPrefix} className="space-y-1">
                                        <div className="flex items-center space-x-1">
                                          <span className="px-1 py-0.5 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-xs">
                                            {originalName}: {originalValue}
                                          </span>
                                          <ArrowRight className="w-3 h-3" />
                                          <span className="text-muted-foreground">Removed</span>
                                        </div>
                                      </div>
                                    );
                                  }
                                  return null;
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  }).filter(Boolean)}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => {
              setExtractionRules([]);
              setSelectedRows(new Set());
              setPreviewData(null);
              setShowPreview(false);
            }}
          >
            Reset
          </Button>
          <Button
            onClick={showPreview ? applyExtraction : generatePreview}
            disabled={extractionRules.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {showPreview ? (
              <>
                <CheckCircle2 className="w-4 h-4 mr-2" />
                Apply Changes
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Preview Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default OptionExtractor;