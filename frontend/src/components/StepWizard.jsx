import React from "react";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import ThemeToggle from "./ThemeToggle";
import { 
  CheckCircle2, 
  Circle, 
  ChevronLeft, 
  ChevronRight,
  User,
  Upload,
  Settings2,
  Table,
  Edit3,
  Download
} from "lucide-react";

const StepWizard = ({ 
  currentStep, 
  totalSteps, 
  onStepChange, 
  canGoNext, 
  canGoPrevious 
}) => {
  const steps = [
    {
      number: 1,
      title: "Business Type",
      description: "Select your merchant category",
      icon: User
    },
    {
      number: 2,
      title: "Upload File",
      description: "Upload your CSV data",
      icon: Upload
    },
    {
      number: 3,
      title: "Process Options",
      description: "Configure option extraction",
      icon: Settings2
    },
    {
      number: 4,
      title: "Review Data",
      description: "Visualize and validate",
      icon: Table
    },
    {
      number: 5,
      title: "Edit Options",
      description: "Modify option types",
      icon: Edit3
    },
    {
      number: 6,
      title: "Export",
      description: "Download final data",
      icon: Download
    }
  ];

  const getStepStatus = (stepNumber) => {
    if (stepNumber < currentStep) return 'completed';
    if (stepNumber === currentStep) return 'current';
    return 'upcoming';
  };

  return (
    <div className="bg-background border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 py-4">
        {/* Step Progress */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-bold text-foreground">
              Product Variant Extractor
            </h1>
            <Badge variant="outline" className="text-xs">
              Step {currentStep} of {totalSteps}
            </Badge>
          </div>
          
          {/* Navigation Buttons */}
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <Button
              variant="outline"
              size="sm"
              onClick={() => onStepChange(currentStep - 1)}
              disabled={!canGoPrevious || currentStep === 1}
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </Button>
            <Button
              size="sm"
              onClick={() => onStepChange(currentStep + 1)}
              disabled={!canGoNext || currentStep === totalSteps}
            >
              Next
              <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>

        {/* Step Indicators */}
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(step.number);
            const Icon = step.icon;
            
            return (
              <div key={step.number} className="flex flex-col items-center flex-1">
                {/* Step Connector Line */}
                {index > 0 && (
                  <div className="w-full h-px bg-muted mb-4 -mt-4 relative">
                    <div 
                      className={`h-full transition-all duration-300 ${
                        status === 'completed' ? 'bg-green-500' : 'bg-muted'
                      }`}
                      style={{ width: status === 'completed' ? '100%' : '0%' }}
                    />
                  </div>
                )}
                
                {/* Step Circle */}
                <div 
                  className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 cursor-pointer ${
                    status === 'completed' 
                      ? 'bg-green-500 text-white' 
                      : status === 'current'
                      ? 'bg-primary text-white ring-4 ring-primary/20'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  onClick={() => {
                    if (status === 'completed' || step.number <= currentStep) {
                      onStepChange(step.number);
                    }
                  }}
                >
                  {status === 'completed' ? (
                    <CheckCircle2 className="w-5 h-5" />
                  ) : (
                    <Icon className="w-5 h-5" />
                  )}
                </div>

                {/* Step Info */}
                <div className="text-center mt-2 max-w-24">
                  <div 
                    className={`text-xs font-medium ${
                      status === 'current' ? 'text-primary' : 'text-foreground'
                    }`}
                  >
                    {step.title}
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 hidden md:block">
                    {step.description}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default StepWizard;