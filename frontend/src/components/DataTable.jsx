import React, { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Badge } from "./ui/badge";
import { compactAllData } from "../utils/dataUtils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./ui/table";
import { 
  X, 
  Edit3, 
  Search, 
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  XCircle
} from "lucide-react";

const ROWS_PER_PAGE = 50;

const DataTable = ({ 
  data, 
  rejectedRows, 
  reviewedRows, 
  generateColor, 
  onReject, 
  onEdit 
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);

  // Helper function to get row status
  const getRowStatus = (rowIndex) => {
    if (rejectedRows.find(r => r.originalIndex === rowIndex)) return 'rejected';
    if (reviewedRows.find(r => r.originalIndex === rowIndex)) return 'reviewed';
    return 'pending';
  };

  // Filter and sort data with compaction applied
  const filteredAndSortedData = useMemo(() => {
    // Apply compaction to ensure no gaps in options display
    const compactedData = compactAllData(data);
    
    let filtered = compactedData.filter(row => 
      Object.values(row).some(value => 
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );

    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aValue = String(a[sortConfig.key] || '');
        const bValue = String(b[sortConfig.key] || '');
        
        if (sortConfig.direction === 'asc') {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    return filtered.map((row, index) => ({
      ...row,
      originalIndex: data.findIndex(originalRow => 
        originalRow.OriginalProductName === row.OriginalProductName &&
        originalRow.BaseProductName === row.BaseProductName
      )
    }));
  }, [data, searchTerm, sortConfig]);

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedData.length / ROWS_PER_PAGE);
  const startIndex = (currentPage - 1) * ROWS_PER_PAGE;
  const paginatedData = filteredAndSortedData.slice(startIndex, startIndex + ROWS_PER_PAGE);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const renderCell = (value) => {
    return value && value.trim() !== "" ? value : "—";
  };

  const renderStatusBadge = (status) => {
    switch (status) {
      case 'rejected':
        return <Badge variant="destructive" className="text-xs"><XCircle className="w-3 h-3 mr-1" />Rejected</Badge>;
      case 'reviewed':
        return <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"><CheckCircle2 className="w-3 h-3 mr-1" />Reviewed</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Pending</Badge>;
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 p-4 bg-muted/30 rounded-lg">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products, options, or values..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="pl-10"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1 h-8 w-8 p-0"
              onClick={() => {
                setSearchTerm('');
                setCurrentPage(1);
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <div className="text-sm text-muted-foreground">
            {searchTerm ? (
              <span>
                Found {filteredAndSortedData.length} of {data.length} products
              </span>
            ) : (
              <span>
                Showing {startIndex + 1}-{Math.min(startIndex + ROWS_PER_PAGE, filteredAndSortedData.length)} of {filteredAndSortedData.length} products
              </span>
            )}
          </div>
          {searchTerm && filteredAndSortedData.length === 0 && (
            <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
              No matches
            </Badge>
          )}
        </div>
      </div>

      {/* Table Container with Horizontal Scroll */}
      <div className="rounded-lg border bg-card overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="sticky top-0 bg-muted z-10">
              <TableRow>
                <TableHead className="w-12 text-center">Status</TableHead>
                
                <TableHead 
                  className="min-w-[200px] cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleSort('OriginalProductName')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Original Product Name</span>
                    <ArrowUpDown className="w-4 h-4" />
                  </div>
                </TableHead>
                
                <TableHead 
                  className="min-w-[200px] cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleSort('BaseProductName')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Base Product Name</span>
                    <ArrowUpDown className="w-4 h-4" />
                  </div>
                </TableHead>

                <TableHead className="min-w-[120px]">Option 1</TableHead>
                <TableHead className="min-w-[120px]">Option 2</TableHead>
                <TableHead className="min-w-[120px]">Option 3</TableHead>
                <TableHead className="w-32 text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            
            <TableBody>
              {paginatedData.map((row, index) => {
                const rowStatus = getRowStatus(row.originalIndex);
                const colorClass = generateColor(row.BaseProductName);
                
                return (
                  <TableRow 
                    key={`${row.originalIndex}-${index}`}
                    className={`${colorClass} hover:bg-muted/50 transition-colors ${
                      rowStatus === 'rejected' ? 'opacity-60' : ''
                    }`}
                  >
                    <TableCell className="text-center">
                      {renderStatusBadge(rowStatus)}
                    </TableCell>
                    
                    <TableCell className="font-medium">
                      {renderCell(row.OriginalProductName)}
                    </TableCell>
                    
                    <TableCell className="font-medium">
                      {renderCell(row.BaseProductName)}
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground font-medium">
                          {renderCell(row.Option1Name)}
                        </div>
                        <div className="text-sm">
                          {renderCell(row.Option1Value)}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground font-medium">
                          {renderCell(row.Option2Name)}
                        </div>
                        <div className="text-sm">
                          {renderCell(row.Option2Value)}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground font-medium">
                          {renderCell(row.Option3Name)}
                        </div>
                        <div className="text-sm">
                          {renderCell(row.Option3Value)}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center justify-center space-x-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(row.originalIndex)}
                          className="h-8 w-8 p-0"
                          title="Edit row"
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onReject(row.originalIndex)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                          title="Reject row"
                          disabled={rowStatus === 'rejected'}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 border-t bg-background">
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataTable;