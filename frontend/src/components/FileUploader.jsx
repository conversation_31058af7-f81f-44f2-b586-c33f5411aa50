import React, { useCallback, useState } from "react";
import { Card, CardContent } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import Papa from "papaparse";
import { Upload, FileText, AlertCircle } from "lucide-react";
import { toast } from "../hooks/use-toast";

const FileUploader = ({ onFileUpload }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const validateCsvHeaders = (headers) => {
    // Required headers: Either "Product Name" or "OriginalProductName" is mandatory
    const hasProductName = headers.some(header => 
      header.toLowerCase().includes('product') && header.toLowerCase().includes('name')
    );
    
    if (!hasProductName) {
      return false;
    }
    
    // Check for variant pattern: Variant Name X, Variant Value X
    const variantPattern = /^Variant (Name|Value) \d+$/i;
    const hasVariantHeaders = headers.some(header => variantPattern.test(header.trim()));
    
    return hasVariantHeaders;
  };

  const transformToStandardFormat = (data) => {
    return data.map(row => {
      // Handle different product name scenarios
      let originalProductName = '';
      let baseProductName = '';
      
      // If OriginalProductName exists, use it
      if (row['OriginalProductName']) {
        originalProductName = row['OriginalProductName'];
        // If there's also a separate "Product Name", use it as base, otherwise use OriginalProductName
        baseProductName = row['Product Name'] || row['OriginalProductName'];
      } else {
        // Find any field that looks like a product name
        const productNameKey = Object.keys(row).find(key => 
          key.toLowerCase().includes('product') && key.toLowerCase().includes('name')
        );
        originalProductName = row[productNameKey] || '';
        baseProductName = row[productNameKey] || '';
      }
      
      const transformedRow = {
        OriginalProductName: originalProductName,
        BaseProductName: baseProductName,
        Option1Name: '',
        Option1Value: '',
        Option2Name: '',
        Option2Value: '',
        Option3Name: '',
        Option3Value: ''
      };

      // Transform variant columns to standard format
      const variantNames = Object.keys(row).filter(key => 
        /^Variant Name \d+$/i.test(key.trim())
      ).sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)[0]);
        const numB = parseInt(b.match(/\d+/)[0]);
        return numA - numB;
      });

      const variantValues = Object.keys(row).filter(key => 
        /^Variant Value \d+$/i.test(key.trim())
      ).sort((a, b) => {
        const numA = parseInt(a.match(/\d+/)[0]);
        const numB = parseInt(b.match(/\d+/)[0]);
        return numA - numB;
      });

      // Map up to 3 variants to our standard format
      for (let i = 0; i < Math.min(3, variantNames.length); i++) {
        const optionNum = i + 1;
        transformedRow[`Option${optionNum}Name`] = row[variantNames[i]] || '';
        transformedRow[`Option${optionNum}Value`] = row[variantValues[i]] || '';
      }

      // Preserve any additional headers (excluding the ones we've already handled)
      Object.keys(row).forEach(key => {
        const keyLower = key.toLowerCase();
        const isProductField = (keyLower.includes('product') && keyLower.includes('name'));
        const isVariantField = /^Variant (Name|Value) \d+$/i.test(key.trim());
        
        if (!isProductField && !isVariantField) {
          transformedRow[key] = row[key];
        }
      });

      return transformedRow;
    });
  };

  const processFile = useCallback((file) => {
    if (!file) return;
    
    if (file.type !== "text/csv" && !file.name.endsWith('.csv')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload a CSV file only.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: false,
      transform: (value) => value ? value.trim() : '',
      transformHeader: (header) => header.trim(),
      error: (error) => {
        // Log error but don't stop processing for field mismatch issues
        console.warn('PapaParse warning:', error);
      },
      complete: (results) => {
        setIsProcessing(false);
        
        if (results.errors.length > 0) {
          // Filter out field mismatch errors as we support flexible CSV formats
          const criticalErrors = results.errors.filter(error => 
            error.type !== 'FieldMismatch' &&
            !error.message.toLowerCase().includes('too many fields') &&
            !error.message.toLowerCase().includes('expected') &&
            !error.message.toLowerCase().includes('field') &&
            error.type !== 'Quotes'
          );
          
          console.log('All CSV errors:', results.errors);
          console.log('Critical CSV errors after filtering:', criticalErrors);
          
          if (criticalErrors.length > 0) {
            toast({
              title: "CSV Parsing Error",
              description: "There were critical errors parsing your CSV file. Please check the format.",
              variant: "destructive"
            });
            console.error("CSV Critical Errors:", criticalErrors);
            return;
          }
        }

        if (!validateCsvHeaders(results.meta.fields)) {
          toast({
            title: "Invalid CSV Structure", 
            description: "CSV must contain a product name column (e.g., 'Product Name' or 'OriginalProductName') and at least one 'Variant Name X' and 'Variant Value X' pair.",
            variant: "destructive"
          });
          return;
        }

        // Transform to standard format and process the data
        const transformedData = transformToStandardFormat(results.data);
        
        // Process the data to ensure empty values are handled
        const processedData = transformedData.map(row => ({
          OriginalProductName: row.OriginalProductName || "",
          BaseProductName: row.BaseProductName || "",
          Option1Name: row.Option1Name || "",
          Option1Value: row.Option1Value || "",
          Option2Name: row.Option2Name || "",
          Option2Value: row.Option2Value || "",
          Option3Name: row.Option3Name || "",
          Option3Value: row.Option3Value || "",
          // Preserve any additional fields
          ...Object.keys(row).reduce((acc, key) => {
            if (!key.startsWith('Original') && !key.startsWith('Base') && !key.startsWith('Option')) {
              acc[key] = row[key] || "";
            }
            return acc;
          }, {})
        }));

        onFileUpload(processedData);
      },
      error: (error) => {
        setIsProcessing(false);
        toast({
          title: "File Processing Error",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  }, [onFileUpload]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFile(files[0]);
    }
  }, [processFile]);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      processFile(file);
    }
  };

  return (
    <div className="space-y-4">
      {/* Drag and Drop Area */}
      <Card
        className={`border-2 border-dashed transition-all duration-200 cursor-pointer hover:border-primary/50 ${
          isDragOver 
            ? "border-primary bg-primary/5 shadow-lg" 
            : "border-muted-foreground/25"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => document.getElementById("csv-file-input").click()}
      >
        <CardContent className="p-8 text-center space-y-4">
          <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
            {isProcessing ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            ) : (
              <Upload className="w-6 h-6 text-primary" />
            )}
          </div>
          
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground">
              {isProcessing ? "Processing CSV..." : "Upload CSV File"}
            </h3>
            <p className="text-sm text-muted-foreground">
              Drag and drop your CSV file here, or click to browse
            </p>
          </div>

          <div className="text-xs text-muted-foreground space-y-1">
            <p>Supported format: .csv files only</p>
            <p>Maximum file size: 100MB</p>
          </div>
        </CardContent>
      </Card>

      {/* Hidden File Input */}
      <Input
        id="csv-file-input"
        type="file"
        accept=".csv"
        onChange={handleFileSelect}
        className="hidden"
        disabled={isProcessing}
      />

      {/* Sample Format Guide */}
      <Card className="bg-muted/30">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <FileText className="w-4 h-4 text-primary mt-0.5" />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-foreground">Expected CSV Format:</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                <p><strong>Required Headers:</strong></p>
                <code className="bg-background px-2 py-1 rounded text-xs block">
                  Product Name, Variant Name 1, Variant Value 1, Variant Name 2, Variant Value 2, Variant Name 3, Variant Value 3
                </code>
                <p className="mt-2"><strong>Additional headers are allowed and will be preserved.</strong></p>
              </div>
              <div className="flex items-center space-x-1 text-xs text-amber-600 dark:text-amber-400">
                <AlertCircle className="w-3 h-3" />
                <span>Empty variant fields will be displayed as "—"</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FileUploader;