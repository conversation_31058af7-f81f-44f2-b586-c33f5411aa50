import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Checkbox } from "./ui/checkbox";
import { <PERSON><PERSON>, <PERSON>R<PERSON>, CheckCircle2, <PERSON>rk<PERSON> } from "lucide-react";
import { toast } from "../hooks/use-toast";

const QuickSetupMode = ({ data, onComplete }) => {
  const [selectedOptions, setSelectedOptions] = useState(new Set());
  const [isProcessing, setIsProcessing] = useState(false);

  // Get all unique option names and their statistics
  const getUniqueOptionNames = () => {
    const optionNames = new Set();
    data.forEach(row => {
      if (row.Option1Name && row.Option1Name.trim()) optionNames.add(row.Option1Name.trim());
      if (row.Option2Name && row.Option2Name.trim()) optionN<PERSON>s.add(row.Option2Name.trim());
      if (row.Option3Name && row.Option3Name.trim()) optionNames.add(row.Option3Name.trim());
    });
    return Array.from(optionNames).sort();
  };

  const getOptionStats = () => {
    const stats = {};
    data.forEach(row => {
      ['Option1', 'Option2', 'Option3'].forEach(optionPrefix => {
        const name = row[`${optionPrefix}Name`];
        const value = row[`${optionPrefix}Value`];
        if (name && name.trim() && value && value.trim()) {
          if (!stats[name]) {
            stats[name] = { count: 0, values: new Set() };
          }
          stats[name].count++;
          stats[name].values.add(value);
        }
      });
    });
    
    return Object.entries(stats).map(([name, data]) => ({
      name,
      count: data.count,
      uniqueValues: data.values.size,
      values: Array.from(data.values).slice(0, 3)
    })).sort((a, b) => b.count - a.count);
  };

  const toggleOptionSelection = (optionName) => {
    const newSelection = new Set(selectedOptions);
    if (newSelection.has(optionName)) {
      newSelection.delete(optionName);
    } else {
      newSelection.add(optionName);
    }
    setSelectedOptions(newSelection);
  };

  const selectAll = () => {
    const allOptions = getUniqueOptionNames();
    setSelectedOptions(new Set(allOptions));
  };

  const selectNone = () => {
    setSelectedOptions(new Set());
  };

  const handleApplyQuickSetup = () => {
    if (uniqueOptions.length - selectedOptions.size === 0) {
      toast({
        title: "No Changes to Apply",
        description: "All options are selected to keep as variants. No extraction will be performed.",
        variant: "destructive"
      });
      return;
    }

    // Show confirmation for destructive actions
    const extractCount = uniqueOptions.length - selectedOptions.size;
    if (extractCount > 3) {
      if (!window.confirm(`This will extract ${extractCount} option types to product names. This action cannot be easily undone. Continue?`)) {
        return;
      }
    }

    setIsProcessing(true);
    
    // Process the data - extract options that are NOT selected to product names
    const processedData = data.map(row => {
      let newRow = { ...row };
      let newBaseProductName = row.BaseProductName || '';

      ['Option1', 'Option2', 'Option3'].forEach(optionPrefix => {
        const name = newRow[`${optionPrefix}Name`];
        const value = newRow[`${optionPrefix}Value`];
        
        if (name && name.trim() && value && value.trim()) {
          // If option is NOT selected to keep, extract it to product name
          if (!selectedOptions.has(name)) {
            // Add to product name
            newBaseProductName = `${newBaseProductName} - ${value}`;
            // Remove the option
            newRow[`${optionPrefix}Name`] = '';
            newRow[`${optionPrefix}Value`] = '';
          }
        }
      });

      newRow.BaseProductName = newBaseProductName.trim();
      return newRow;
    });

    setTimeout(() => {
      onComplete(processedData);
      setIsProcessing(false);
      
      toast({
        title: "Quick Setup Applied",
        description: `Extracted ${extractCount} option types to product names. ${selectedOptions.size} options kept as variants.`,
      });
    }, 1500); // Slightly longer for better UX
  };

  const optionStats = getOptionStats();
  const uniqueOptions = getUniqueOptionNames();

  return (
    <Card className="border-2 border-orange-200 bg-orange-50/30 dark:bg-orange-950/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-orange-600" />
            <CardTitle className="text-orange-900 dark:text-orange-100">
              Quick Setup Mode
            </CardTitle>
          </div>
          <Badge variant="outline" className="text-xs bg-orange-100 dark:bg-orange-900">
            Smart Configuration
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Choose which options to keep as variants. Unselected options will be extracted to product names automatically.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Selection Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm" onClick={selectAll}>
              Select All
            </Button>
            <Button variant="outline" size="sm" onClick={selectNone}>
              Select None
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            {selectedOptions.size} of {uniqueOptions.length} options selected to keep
          </div>
        </div>

        {/* Option Selection Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {optionStats.map((option, index) => {
            const isSelected = selectedOptions.has(option.name);
            return (
              <div 
                key={index}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  isSelected 
                    ? 'border-green-300 bg-green-50 dark:bg-green-950/20' 
                    : 'border-gray-200 bg-white dark:bg-gray-800 hover:border-gray-300'
                }`}
                onClick={() => toggleOptionSelection(option.name)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      checked={isSelected}
                      onChange={() => toggleOptionSelection(option.name)}
                    />
                    <span className="font-medium text-sm">{option.name}</span>
                  </div>
                  <div className="flex flex-col items-end">
                    <Badge variant="secondary" className="text-xs mb-1">
                      {option.count} items
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {option.uniqueValues} values
                    </span>
                  </div>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  Values: {option.values.join(', ')}
                  {option.values.length < option.uniqueValues && '...'}
                </div>
                
                <div className="mt-2 text-xs">
                  {isSelected ? (
                    <span className="text-green-600 font-medium flex items-center">
                      <CheckCircle2 className="w-3 h-3 mr-1" />
                      Keep as option
                    </span>
                  ) : (
                    <span className="text-orange-600 font-medium flex items-center">
                      <Sparkles className="w-3 h-3 mr-1" />
                      Extract to name
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Preview */}
        <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Quick Setup Preview:
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-green-700 dark:text-green-300 font-medium mb-1">
                ✓ Options to Keep ({selectedOptions.size}):
              </div>
              {selectedOptions.size > 0 ? (
                <div className="text-xs text-muted-foreground">
                  {Array.from(selectedOptions).join(', ')}
                </div>
              ) : (
                <div className="text-xs text-muted-foreground italic">
                  None selected - all options will be extracted
                </div>
              )}
            </div>
            <div>
              <div className="text-orange-700 dark:text-orange-300 font-medium mb-1">
                → Extract to Names ({uniqueOptions.length - selectedOptions.size}):
              </div>
              {uniqueOptions.length - selectedOptions.size > 0 ? (
                <div className="text-xs text-muted-foreground">
                  {uniqueOptions.filter(opt => !selectedOptions.has(opt)).join(', ')}
                </div>
              ) : (
                <div className="text-xs text-muted-foreground italic">
                  None - all options will be kept as variants
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-xs text-muted-foreground">
            Need more control? Switch to <strong>Advanced mode</strong> using the toggle above.
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setSelectedOptions(new Set())}
              disabled={isProcessing}
            >
              Reset
            </Button>
            <Button
              onClick={handleApplyQuickSetup}
              disabled={isProcessing}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4" />
                  <span>Apply Quick Setup</span>
                  <ArrowRight className="w-4 h-4" />
                </div>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickSetupMode;