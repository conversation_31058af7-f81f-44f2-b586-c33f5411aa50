import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { 
  Beef, 
  Bike, 
  Gem, 
  Shirt, 
  Wine, 
  Leaf,
  ArrowRight,
  CheckCircle2
} from "lucide-react";

const MerchantTypeSelector = ({ selectedType, onTypeSelect, onNext }) => {
  const merchantTypes = [
    {
      id: 'food-meat',
      name: 'Food & Meat Products',
      icon: Beef,
      description: 'Restaurants, butchers, food manufacturers',
      examples: ['Organic Beef Steak', 'Fresh Salmon Fillet', 'Artisan Bread'],
      color: 'bg-red-50 dark:bg-red-950/20 border-red-200 hover:bg-red-100',
      iconColor: 'text-red-600'
    },
    {
      id: 'bicycle-sports',
      name: 'Bicycle & Sports Footwear',
      icon: Bike,
      description: 'Sports retailers, bike shops, athletic wear',
      examples: ['Mountain Bike 27"', 'Running Shoes Size 10', 'Cycling Helmet'],
      color: 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 hover:bg-blue-100',
      iconColor: 'text-blue-600'
    },
    {
      id: 'jewelry',
      name: 'Jewelry',
      icon: Gem,
      description: 'Jewelers, accessories, luxury goods',
      examples: ['Diamond Ring 14K Gold', 'Silver Necklace 18"', 'Pearl Earrings'],
      color: 'bg-purple-50 dark:bg-purple-950/20 border-purple-200 hover:bg-purple-100',
      iconColor: 'text-purple-600'
    },
    {
      id: 'apparel',
      name: 'Apparel',
      icon: Shirt,
      description: 'Fashion retailers, clothing brands',
      examples: ['Cotton T-Shirt Size L', 'Denim Jeans 32"', 'Summer Dress'],
      color: 'bg-green-50 dark:bg-green-950/20 border-green-200 hover:bg-green-100',
      iconColor: 'text-green-600'
    },
    {
      id: 'wine',
      name: 'Wine',
      icon: Wine,
      description: 'Wineries, liquor stores, distributors',
      examples: ['Cabernet Sauvignon 2020', 'Chardonnay Reserve', 'Champagne Brut'],
      color: 'bg-amber-50 dark:bg-amber-950/20 border-amber-200 hover:bg-amber-100',
      iconColor: 'text-amber-600'
    },
    {
      id: 'cbd',
      name: 'CBD Products',
      icon: Leaf,
      description: 'CBD retailers, wellness products',
      examples: ['CBD Oil 1000mg', 'Hemp Gummies', 'Topical Cream'],
      color: 'bg-emerald-50 dark:bg-emerald-950/20 border-emerald-200 hover:bg-emerald-100',
      iconColor: 'text-emerald-600'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Select Your Business Type
        </h2>
        <p className="text-muted-foreground">
          Choose the category that best describes your products to optimize the review process
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {merchantTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = selectedType === type.id;
          
          return (
            <Card
              key={type.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'ring-2 ring-primary shadow-lg scale-105'
                  : type.color
              }`}
              onClick={() => onTypeSelect(type.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg ${type.color} flex items-center justify-center`}>
                      <Icon className={`w-5 h-5 ${type.iconColor}`} />
                    </div>
                    <div>
                      <CardTitle className="text-sm font-semibold">
                        {type.name}
                      </CardTitle>
                    </div>
                  </div>
                  {isSelected && (
                    <CheckCircle2 className="w-5 h-5 text-primary" />
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <p className="text-xs text-muted-foreground mb-3">
                  {type.description}
                </p>
                
                <div className="space-y-2">
                  <div className="text-xs font-medium text-foreground">
                    Example Products:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {type.examples.slice(0, 2).map((example, index) => (
                      <Badge key={index} variant="secondary" className="text-xs px-2 py-1">
                        {example}
                      </Badge>
                    ))}
                    <Badge variant="outline" className="text-xs px-2 py-1">
                      +more
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedType && (
        <div className="flex justify-center pt-6">
          <Button 
            onClick={onNext}
            className="px-8 py-2 bg-primary hover:bg-primary/90"
          >
            Continue to File Upload
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      )}

      {!selectedType && (
        <div className="text-center pt-4">
          <p className="text-sm text-muted-foreground">
            Please select a business type to continue
          </p>
        </div>
      )}
    </div>
  );
};

export default MerchantTypeSelector;