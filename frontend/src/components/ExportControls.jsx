import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import <PERSON> from "papaparse";
import { Download, FileX, <PERSON><PERSON>he<PERSON> } from "lucide-react";
import { toast } from "../hooks/use-toast";

const ExportControls = ({ rejectedRows, reviewedRows }) => {
  
  const downloadCsv = (data, filename) => {
    if (data.length === 0) {
      toast({
        title: "No Data to Export",
        description: `There are no ${filename.includes('rejected') ? 'rejected' : 'reviewed'} rows to export.`,
        variant: "destructive"
      });
      return;
    }

    // Convert data back to CSV format
    const csvData = data.map(row => ({
      OriginalProductName: row.OriginalProductName || "",
      BaseProductName: row.BaseProductName || "",
      Option1Name: row.Option1Name || "",
      Option1Value: row.Option1Value || "",
      Option2Name: row.Option2Name || "",
      Option2Value: row.Option2Value || "",
      Option3Name: row.Option3Name || "",
      Option3Value: row.Option3Value || ""
    }));

    const csv = Papa.unparse(csvData, {
      header: true,
      skipEmptyLines: false
    });

    // Create download link
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: `${filename} has been downloaded successfully.`,
      });
    } else {
      toast({
        title: "Export Failed",
        description: "Your browser doesn't support file downloads.",
        variant: "destructive"
      });
    }
  };

  const handleExportRejected = () => {
    const timestamp = new Date().toISOString().slice(0, 10);
    downloadCsv(rejectedRows, `rejected_products_${timestamp}.csv`);
  };

  const handleExportReviewed = () => {
    const timestamp = new Date().toISOString().slice(0, 10);
    downloadCsv(reviewedRows, `reviewed_products_${timestamp}.csv`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Download className="w-5 h-5" />
          <span>Export Data</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Export Rejected */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-foreground">
                Rejected Products
              </h3>
              <Badge 
                variant="destructive" 
                className="text-xs"
              >
                {rejectedRows.length} rows
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Export all products that were rejected without any modifications.
            </p>
            <Button
              onClick={handleExportRejected}
              disabled={rejectedRows.length === 0}
              variant="outline"
              size="sm"
              className="w-full text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 border-red-200 dark:border-red-800"
            >
              <FileX className="w-4 h-4 mr-2" />
              Export Rejected CSV
            </Button>
          </div>

          {/* Export Reviewed */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-foreground">
                Reviewed Products  
              </h3>
              <Badge 
                variant="default"
                className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
              >
                {reviewedRows.length} rows
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Export all products that were edited and reviewed with your changes.
            </p>
            <Button
              onClick={handleExportReviewed}
              disabled={reviewedRows.length === 0}
              variant="outline"
              size="sm"
              className="w-full text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-950 border-green-200 dark:border-green-800"
            >
              <FileCheck className="w-4 h-4 mr-2" />
              Export Reviewed CSV
            </Button>
          </div>
        </div>

        {/* Export Summary */}
        {(rejectedRows.length > 0 || reviewedRows.length > 0) && (
          <div className="mt-6 p-4 bg-muted/30 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {rejectedRows.length}
                </div>
                <div className="text-muted-foreground">Rejected</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {reviewedRows.length}
                </div>
                <div className="text-muted-foreground">Reviewed</div>
              </div>
            </div>
          </div>
        )}

        {/* Export Tips */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Export Tips:
          </h4>
          <ul className="text-xs text-blue-700 dark:text-blue-200 space-y-1">
            <li>• Files are automatically timestamped</li>
            <li>• CSV format matches the original upload structure</li>
            <li>• Empty option fields are preserved as blank values</li>
            <li>• Both files can be imported back into any CSV tool</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default ExportControls;