// Utility functions for data processing and compaction

/**
 * Compacts options in a single row by removing gaps
 * @param {Object} rowData - The row data object
 * @returns {Object} - Row data with compacted options
 */
export const compactRowOptions = (rowData) => {
  const options = [
    { name: rowData.Option1Name || "", value: rowData.Option1Value || "" },
    { name: rowData.Option2Name || "", value: rowData.Option2Value || "" },
    { name: rowData.Option3Name || "", value: rowData.Option3Value || "" }
  ];

  // Filter out completely empty options (both name and value empty or just whitespace)
  const validOptions = options.filter(option => 
    (option.name && option.name.trim()) || (option.value && option.value.trim())
  );

  // Create the compacted row data
  const compactedData = { ...rowData };
  
  // Reset all options
  compactedData.Option1Name = "";
  compactedData.Option1Value = "";
  compactedData.Option2Name = "";
  compactedData.Option2Value = "";
  compactedData.Option3Name = "";
  compactedData.Option3Value = "";

  // Fill in the valid options sequentially starting from Option1
  validOptions.forEach((option, index) => {
    if (index < 3) { // Only support up to 3 options
      compactedData[`Option${index + 1}Name`] = option.name.trim();
      compactedData[`Option${index + 1}Value`] = option.value.trim();
    }
  });

  return compactedData;
};

/**
 * Compacts options for an array of data rows
 * @param {Array} dataArray - Array of row data objects
 * @returns {Array} - Array of row data with compacted options
 */
export const compactAllData = (dataArray) => {
  return dataArray.map(row => compactRowOptions(row));
};

/**
 * Gets comprehensive statistics about the data
 * @param {Array} data - Array of row data
 * @param {Array} reviewedRows - Array of reviewed rows  
 * @param {Array} rejectedRows - Array of rejected rows
 * @returns {Object} - Statistics object
 */
export const getDataStatistics = (data, reviewedRows = [], rejectedRows = []) => {
  const compactedData = compactAllData(data);
  
  // Basic counts
  const totalProducts = data.length;
  const reviewedCount = reviewedRows.length;
  const rejectedCount = rejectedRows.length;
  const pendingCount = totalProducts - reviewedCount - rejectedCount;
  
  // Option usage statistics
  const optionStats = {
    option1Used: 0,
    option2Used: 0, 
    option3Used: 0,
    totalOptionsUsed: 0
  };
  
  const optionNameCounts = {};
  const optionValueCounts = {};
  const uniqueBaseProducts = new Set();
  
  compactedData.forEach(row => {
    // Count option usage
    if (row.Option1Name && row.Option1Name.trim()) {
      optionStats.option1Used++;
      optionStats.totalOptionsUsed++;
      
      // Count option name frequency
      const optionName = row.Option1Name.trim();
      optionNameCounts[optionName] = (optionNameCounts[optionName] || 0) + 1;
      
      // Count option value frequency  
      if (row.Option1Value && row.Option1Value.trim()) {
        const key = `${optionName}:${row.Option1Value.trim()}`;
        optionValueCounts[key] = (optionValueCounts[key] || 0) + 1;
      }
    }
    
    if (row.Option2Name && row.Option2Name.trim()) {
      optionStats.option2Used++;
      optionStats.totalOptionsUsed++;
      
      const optionName = row.Option2Name.trim();
      optionNameCounts[optionName] = (optionNameCounts[optionName] || 0) + 1;
      
      if (row.Option2Value && row.Option2Value.trim()) {
        const key = `${optionName}:${row.Option2Value.trim()}`;
        optionValueCounts[key] = (optionValueCounts[key] || 0) + 1;
      }
    }
    
    if (row.Option3Name && row.Option3Name.trim()) {
      optionStats.option3Used++;
      optionStats.totalOptionsUsed++;
      
      const optionName = row.Option3Name.trim();
      optionNameCounts[optionName] = (optionNameCounts[optionName] || 0) + 1;
      
      if (row.Option3Value && row.Option3Value.trim()) {
        const key = `${optionName}:${row.Option3Value.trim()}`;
        optionValueCounts[key] = (optionValueCounts[key] || 0) + 1;
      }
    }
    
    // Count unique base products
    if (row.BaseProductName && row.BaseProductName.trim()) {
      uniqueBaseProducts.add(row.BaseProductName.trim());
    }
  });
  
  // Calculate completion percentage
  const completionRate = totalProducts > 0 ? 
    Math.round(((reviewedCount + rejectedCount) / totalProducts) * 100) : 0;
  
  return {
    // Basic statistics
    totalProducts,
    reviewedCount,
    rejectedCount, 
    pendingCount,
    completionRate,
    
    // Product statistics
    uniqueBaseProducts: uniqueBaseProducts.size,
    avgOptionsPerProduct: totalProducts > 0 ? 
      Math.round((optionStats.totalOptionsUsed / totalProducts) * 10) / 10 : 0,
    
    // Option usage statistics  
    optionStats,
    
    // Most common option names
    topOptionNames: Object.entries(optionNameCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([name, count]) => ({ name, count })),
      
    // Most common option values
    topOptionValues: Object.entries(optionValueCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([nameValue, count]) => {
        const [name, value] = nameValue.split(':');
        return { name, value, count };
      }),
      
    // Data quality metrics
    dataQuality: {
      emptyBaseProductNames: compactedData.filter(row => 
        !row.BaseProductName || !row.BaseProductName.trim()).length,
      emptyOriginalProductNames: compactedData.filter(row => 
        !row.OriginalProductName || !row.OriginalProductName.trim()).length,
      incompleteOptions: compactedData.filter(row => 
        (row.Option1Name && row.Option1Name.trim() && (!row.Option1Value || !row.Option1Value.trim())) ||
        (row.Option2Name && row.Option2Name.trim() && (!row.Option2Value || !row.Option2Value.trim())) ||
        (row.Option3Name && row.Option3Name.trim() && (!row.Option3Value || !row.Option3Value.trim()))
      ).length
    }
  };
};