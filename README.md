# Variant Extractor

A modern React-based web application for processing and analyzing CSV data with variant extraction capabilities. This application provides an intuitive interface for uploading, reviewing, and exporting CSV data with advanced filtering and processing options.

## Features

- **CSV File Upload & Processing**: Upload and process CSV files with real-time preview
- **Data Review Interface**: Interactive data table with sorting, filtering, and editing capabilities
- **Variant Extraction**: Advanced algorithms for extracting and analyzing data variants
- **Export Controls**: Flexible export options with customizable formats
- **Responsive Design**: Modern UI built with React, Tailwind CSS, and Radix UI components
- **Theme Support**: Light and dark theme toggle
- **Step-by-Step Wizard**: Guided workflow for data processing tasks

## Architecture

- **Frontend**: React 19 with Create React App, Tailwind CSS, and Radix UI
- **Build Tool**: CRACO (Create React App Configuration Override)
- **Deployment**: Docker with multi-stage build and nginx serving
- **Backend**: FastAPI with MongoDB (separate service)

## Prerequisites

Before running this application, ensure you have the following installed:

- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning the repository

### System Requirements

- **RAM**: Minimum 2GB available
- **Disk Space**: At least 1GB free space
- **Network**: Internet connection for downloading dependencies

## Quick Start with Docker

### 1. Clone the Repository

```bash
git clone <repository-url>
cd variant-extractor
```

### 2. Build and Run with Docker Compose

```bash
# Build and start the application
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

### 3. Access the Application

Once the containers are running, open your web browser and navigate to:

```
http://localhost:80
```

The application will be served by nginx and should load immediately.

### 4. Stop the Application

```bash
# Stop the containers
docker-compose down

# Stop and remove volumes (if needed)
docker-compose down -v
```

## Docker Configuration Details

### Multi-Stage Build Process

The Docker setup uses a multi-stage build approach for optimal production deployment:

1. **Build Stage**: Uses Node.js 20 LTS Alpine image to build the React application
2. **Production Stage**: Uses nginx Alpine image to serve the built static files

### Build Optimization Features

- **Layer Caching**: Optimized layer ordering for faster rebuilds
- **Production Dependencies**: Only production dependencies are included
- **Gzip Compression**: Automatic compression for better performance
- **Security Headers**: Comprehensive security headers configuration
- **Health Checks**: Built-in health monitoring for container orchestration

### Port Configuration

- **Application Port**: 80 (HTTP)
- **Container Internal Port**: 80 (nginx)

## Development Setup

If you prefer to run the application in development mode without Docker:

### Prerequisites for Development

- **Node.js**: Version 20.x LTS
- **Yarn**: Version 1.22 or higher

### Development Commands

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
yarn install

# Start development server
yarn start

# Build for production
yarn build

# Run tests
yarn test
```

The development server will start on `http://localhost:3000`.

## Configuration

### Environment Variables

The application supports the following environment variables:

- `NODE_ENV`: Set to `production` for production builds
- `REACT_APP_API_URL`: Backend API URL (if backend integration is needed)

### nginx Configuration

The nginx configuration includes:

- **SPA Routing**: Proper handling of React Router routes
- **Static Asset Caching**: Optimized caching for CSS, JS, and image files
- **Security Headers**: XSS protection, content type options, and frame options
- **Gzip Compression**: Automatic compression for supported file types
- **Health Check Endpoint**: `/health` endpoint for monitoring

## Troubleshooting

### Common Issues

1. **Port 80 Already in Use**
   ```bash
   # Check what's using port 80
   sudo lsof -i :80

   # Stop the conflicting service or use a different port
   docker-compose up --build -p 8080:80
   ```

2. **Build Failures**
   ```bash
   # Clean Docker cache and rebuild
   docker system prune -a
   docker-compose build --no-cache
   ```

3. **Permission Issues**
   ```bash
   # Ensure Docker has proper permissions
   sudo usermod -aG docker $USER
   # Log out and back in, then try again
   ```

### Health Checks

The application includes built-in health checks:

- **Container Health**: `docker-compose ps` to check container status
- **Application Health**: Visit `http://localhost:80/health` for nginx health
- **Build Logs**: `docker-compose logs frontend` for detailed logs

## Production Deployment

### Security Considerations

- The nginx configuration includes security headers
- Static files are served with appropriate caching headers
- The application runs as a non-root user in the container

### Performance Optimizations

- Multi-stage build reduces final image size
- Gzip compression is enabled for all text-based assets
- Static assets are cached with long expiration times
- nginx is configured with performance optimizations

### Monitoring

- Health check endpoints are available at `/health`
- nginx access and error logs are available via `docker-compose logs`
- Container metrics can be monitored using Docker stats

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with Docker: `docker-compose up --build`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
