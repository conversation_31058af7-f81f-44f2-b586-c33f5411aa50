# Dependencies
frontend/node_modules
backend/__pycache__
backend/.pytest_cache

# Build outputs
frontend/build
frontend/dist

# Development files
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
backend/.env

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
frontend/README.md
*.md

# Test files
tests/
frontend/src/**/*.test.js
frontend/src/**/*.spec.js

# Temporary files
tmp/
temp/
